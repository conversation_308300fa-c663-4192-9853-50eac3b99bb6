#pragma once

#include <cuda_runtime.h>
#include <cstdio>
#include <string>

// Simple debug logging macros for KV buffer operations
// Set ENABLE_KV_DEBUG=1 to enable detailed debugging logs
#ifndef ENABLE_KV_DEBUG
#define ENABLE_KV_DEBUG 1
#endif

#if ENABLE_KV_DEBUG
#ifndef LOG_PRINTF
#define LOG_PRINTF(...) printf(__VA_ARGS__)
#endif
#else
#ifndef LOG_PRINTF
#define LOG_PRINTF(...) do {} while(0)
#endif
#endif

// CUDA error checking macro with detailed logging
#define CUDA_CHECK_DEBUG(call, context) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        LOG_PRINTF("[CUDA_ERROR] %s failed in %s: %s (error code: %d)\n", \
                     #call, context, cudaGetErrorString(err), err); \
    } \
} while(0)

// Simple memory validation utilities
namespace debug_utils {

// Check if a CUDA pointer is valid and accessible
inline bool is_cuda_pointer_valid(void* ptr, size_t size) {
    if (ptr == nullptr) {
        LOG_PRINTF("[DEBUG] Pointer is null\n");
        return false;
    }

    cudaPointerAttributes attrs;
    cudaError_t err = cudaPointerGetAttributes(&attrs, ptr);
    if (err != cudaSuccess) {
        LOG_PRINTF("[DEBUG] cudaPointerGetAttributes failed for ptr=%p: %s\n",
                     ptr, cudaGetErrorString(err));
        return false;
    }

    // Check if it's a device pointer
    if (attrs.type != cudaMemoryTypeDevice) {
        LOG_PRINTF("[DEBUG] Pointer %p is not a device pointer (type=%d)\n",
                     ptr, attrs.type);
        return false;
    }

    return true;
}

// Simple tensor logging macros to avoid template issues
#define LOG_TENSOR_BASIC_INFO(tensor, name) do { \
    LOG_PRINTF("[TENSOR_DEBUG] %s: device=%d, contiguous=%s, data_ptr=%p\n", \
                 name, (tensor).device().index(), \
                 (tensor).is_contiguous() ? "true" : "false", \
                 (void*)(tensor).data_ptr()); \
} while(0)

#define LOG_TENSOR_SHAPE(tensor, name) do { \
    LOG_PRINTF("[TENSOR_DEBUG] %s shape: [", name); \
    for (int i = 0; i < (tensor).dim(); ++i) { \
        LOG_PRINTF("%ld", (long)(tensor).size(i)); \
        if (i < (tensor).dim() - 1) LOG_PRINTF(", "); \
    } \
    LOG_PRINTF("]\n"); \
} while(0)

// Simple validation macros
#define VALIDATE_ARRAY_ACCESS(index, array_size, context) do { \
    if ((index) < 0 || (index) >= (array_size)) { \
        LOG_PRINTF("[BOUNDS_ERROR] %s: index %ld out of bounds [0, %ld)\n", \
                     context, (long)(index), (long)(array_size)); \
    } \
} while(0)

// Log CUDA stream status
inline void log_stream_status(cudaStream_t stream, const char* context) {
    cudaError_t status = cudaStreamQuery(stream);
    LOG_PRINTF("[STREAM_DEBUG] %s: stream=%p, status=%s\n",
                 context, stream, cudaGetErrorString(status));
}

// Log CUDA device memory info
inline void log_device_memory_info(int device_id) {
    size_t free_bytes, total_bytes;
    cudaError_t err = cudaMemGetInfo(&free_bytes, &total_bytes);
    if (err == cudaSuccess) {
        size_t used_bytes = total_bytes - free_bytes;
        LOG_PRINTF("[MEMORY_DEBUG] Device %d: Used=%.2f MB, Free=%.2f MB, Total=%.2f MB\n",
                     device_id,
                     used_bytes / (1024.0 * 1024.0),
                     free_bytes / (1024.0 * 1024.0),
                     total_bytes / (1024.0 * 1024.0));
    } else {
        LOG_PRINTF("[MEMORY_DEBUG] Failed to get memory info for device %d: %s\n",
                     device_id, cudaGetErrorString(err));
    }
}

// Simple slot mapping validation
#define VALIDATE_SLOT_MAPPING_SAMPLE(slot_mapping, num_tokens, block_size, context) do { \
    LOG_PRINTF("[SLOT_DEBUG] %s: Validating slot mapping for %d tokens, block_size=%d\n", \
                 context, num_tokens, block_size); \
    for (int i = 0; i < std::min((int64_t)5, num_tokens); ++i) { \
        int64_t slot = (slot_mapping)[i]; \
        if (slot >= 0) { \
            int64_t block_idx = slot / block_size; \
            int64_t block_offset = slot % block_size; \
            LOG_PRINTF("[SLOT_DEBUG] %s: token %d -> slot %ld (block_idx=%ld, block_offset=%ld)\n", \
                         context, i, slot, block_idx, block_offset); \
            if (block_offset >= block_size) { \
                LOG_PRINTF("[SLOT_ERROR] %s: token %d has invalid slot %ld (block_offset=%ld >= block_size=%d)\n", \
                             context, i, slot, block_offset, block_size); \
            } \
        } \
    } \
} while(0)

// Simple cache pointer logging
#define LOG_CACHE_POINTERS(cache_ptrs, num_layers, name) do { \
    LOG_PRINTF("[CACHE_PTR_DEBUG] %s cache pointers:\n", name); \
    for (int i = 0; i < std::min(5, num_layers); ++i) { \
        LOG_PRINTF("  Layer %d: %p", i, (cache_ptrs)[i]); \
        if ((cache_ptrs)[i] == nullptr) { \
            LOG_PRINTF(" (NULL!)"); \
        } \
        LOG_PRINTF("\n"); \
    } \
    if (num_layers > 5) { \
        LOG_PRINTF("  ... and %d more layers\n", num_layers - 5); \
    } \
} while(0)

// Simple kernel input validation macros
#define VALIDATE_BASIC_POINTERS(keys_ptr, values_ptr, slot_mapping_ptr, context) do { \
    if (!(keys_ptr) || !(values_ptr) || !(slot_mapping_ptr)) { \
        LOG_PRINTF("[VALIDATION_ERROR] %s: Null input tensor pointers: keys=%p, values=%p, slot_mapping=%p\n", \
                     context, (keys_ptr), (values_ptr), (slot_mapping_ptr)); \
    } \
} while(0)

#define VALIDATE_CACHE_POINTERS(key_cache_ptrs, value_cache_ptrs, context) do { \
    if (!(key_cache_ptrs) || !(value_cache_ptrs)) { \
        LOG_PRINTF("[VALIDATION_ERROR] %s: Null cache pointer arrays: key_cache_ptrs=%p, value_cache_ptrs=%p\n", \
                     context, (key_cache_ptrs), (value_cache_ptrs)); \
    } \
} while(0)

#define VALIDATE_DIMENSIONS(num_layers, num_tokens, num_heads, head_size, context) do { \
    if ((num_layers) <= 0 || (num_tokens) <= 0 || (num_heads) <= 0 || (head_size) <= 0) { \
        LOG_PRINTF("[VALIDATION_ERROR] %s: Invalid dimensions: layers=%d, tokens=%d, heads=%d, head_size=%d\n", \
                     context, num_layers, num_tokens, num_heads, head_size); \
    } \
} while(0)

#define VALIDATE_BLOCK_PARAMS(block_size, block_stride, context) do { \
    if ((block_size) <= 0 || (block_stride) <= 0) { \
        LOG_PRINTF("[VALIDATION_ERROR] %s: Invalid block parameters: block_size=%d, block_stride=%d\n", \
                     context, block_size, block_stride); \
    } \
} while(0)

} // namespace debug_utils
