import bisect
from asyncio import Event
from quart import Quart, make_response, request
from typing import List, Dict, Tu<PERSON>, Any, Optional, AsyncGenerator
import uuid
import math
from sklearn.linear_model import SGDRegressor
import numpy as np
import time
import json
from abc import ABC, abstractmethod
from pydantic import BaseModel, model_validator
from transformers import AutoTokenizer
from dataclasses import dataclass
from prettytable import PrettyTable
from copy import deepcopy

from vllm.logger import init_logger


logger = init_logger(__name__)

RequestData = Dict[str, Any]

class GlobalSchedulerConfig(BaseModel):
    model: str
    world_size: int
    decoding_size: int

    proxy_port: int
    instance_ports: List[int]
    # Optional list of IP addresses corresponding to instance_ports.
    # If not provided, defaults to localhost for all instances.
    instance_ips: List[str]

    scheduler_type: str
    schedule_policy: str
    enable_zero_decoding: bool
    prefill_token_limit: List[int]
    decoding_policy: str

    load_aware_buffer_length: int

    slo_dict: Dict[str, tuple[float, float]]

    group_sizes: List[int]

    prefill_processing_capacities: List[float]

    @model_validator(mode="before")
    def sanity_check(cls, values):
        assert (
            0 <= values["decoding_size"] < values["world_size"]
        ), "Invalid decoding size"
        assert values["scheduler_type"] in [
            "chunked_prefill",
            "pd_disaggregation",
            "hybrid",
        ], "Invalid scheduler type"
        assert values["decoding_policy"] in [
            "",
            "simple_flowing",
            "prophet_flowing",
        ], "Invalid decoding policy"
        # Validate instance_ips if provided
        ips = values["instance_ips"]
        assert (
            len(ips) >= values["world_size"]
        ), f"instance_ips length {len(ips)} < world_size {values['world_size']}"
        return values


@dataclass
class RequestMetrics:
    request_id: Optional[str] = None
    prefill_length: Optional[int] = None
    arrival_time: Optional[float] = None
    scheduled_time: Optional[float] = None
    prefill_instance: Optional[int] = None
    prefill_end_time: Optional[float] = None
    first_token_time: Optional[float] = None
    decoding_end_time: Optional[float] = None

    def record_request_id(self, request_id: str):
        assert self.request_id is None
        self.request_id = request_id

    def record_prefill_length(self, prefill_length: int):
        assert self.prefill_length is None
        self.prefill_length = prefill_length

    def record_arrival_time(self):
        assert self.arrival_time is None
        self.arrival_time = time.time()

    def record_scheduled_time(self, prefill_instance: int):
        assert self.scheduled_time is None
        self.scheduled_time = time.time()
        self.prefill_instance = prefill_instance

    def record_prefill_end_time(self):
        assert self.prefill_end_time is None
        self.prefill_end_time = time.time()

    def record_first_token_time(self):
        assert self.first_token_time is None
        self.first_token_time = time.time()

    def record_decoding_end_time(self):
        assert self.decoding_end_time is None
        self.decoding_end_time = time.time()

    def get_table_head(self):
        return [
            "Request ID",
            "Prefill Length",
            "Prefill Instance",
            "Schedule Elapsed",
            "Prefill Elapsed",
            "Transfer Elapsed",
            "TTFT",
            "Decoding Elapsed",
        ]

    def get_row_data(self):
        assert self.arrival_time is not None
        assert self.scheduled_time is not None
        assert self.prefill_end_time is not None
        assert self.first_token_time is not None
        assert self.decoding_end_time is not None
        schedule_elapsed = self.scheduled_time - self.arrival_time
        prefill_elapsed = self.prefill_end_time - self.scheduled_time
        transfer_elapsed = self.first_token_time - self.prefill_end_time
        ttft = self.first_token_time - self.arrival_time
        decoding_elapsed = self.decoding_end_time - self.first_token_time
        return [
            self.request_id,
            self.prefill_length,
            self.prefill_instance,
            (
                f"{schedule_elapsed:.3f}",
                "Warning:Schedule" if schedule_elapsed > 1 else "",
            ),
            (
                f"{prefill_elapsed:.3f}",
                "Warning:Prefill" if prefill_elapsed > 1 else "",
            ),
            (
                f"{transfer_elapsed:.3f}",
                "Warning:Transfer" if transfer_elapsed > 1 else "",
            ),
            (f"{ttft:.3f}", "Warning:TTFT" if ttft > 5 else ""),
            f"{decoding_elapsed:.3f}",
        ]

    def get_json_data(self):
        """返回与 get_table_head 相同项目的 JSON 格式数据"""
        assert self.arrival_time is not None
        assert self.scheduled_time is not None
        assert self.prefill_end_time is not None
        assert self.first_token_time is not None
        assert self.decoding_end_time is not None

        schedule_elapsed = self.scheduled_time - self.arrival_time
        prefill_elapsed = self.prefill_end_time - self.scheduled_time
        transfer_elapsed = self.first_token_time - self.prefill_end_time
        ttft = self.first_token_time - self.arrival_time
        decoding_elapsed = self.decoding_end_time - self.first_token_time

        return {
            "request_id": self.request_id,
            "prefill_length": self.prefill_length,
            "prefill_instance": self.prefill_instance,
            "schedule_elapsed": f"{schedule_elapsed:.3f}",
            "prefill_elapsed": f"{prefill_elapsed:.3f}",
            "transfer_elapsed": f"{transfer_elapsed:.3f}",
            "ttft": f"{ttft:.3f}",
            "decoding_elapsed": f"{decoding_elapsed:.3f}",
        }


class GlobalRequest:
    """维护一个全局请求的属性, 例如请求字段信息和执行信息
    由 Proxy 创建, 有全局唯一 ID
    方法:
    - 提供字段维护接口.
    - 生成实例请求.
    """

    allocated_id_num: int = 0
    config: Optional[GlobalSchedulerConfig] = None
    tokenizer: Optional[AutoTokenizer] = None
    TOKEN_MAX_VALUE: int = 50256

    @staticmethod
    def init_class(config: GlobalSchedulerConfig):
        GlobalRequest.config = config
        GlobalRequest.tokenizer = AutoTokenizer.from_pretrained(config.model)
        GlobalRequest.TOKEN_MAX_VALUE = GlobalRequest.tokenizer.vocab_size - 1

    @staticmethod
    def create(request_data: RequestData, slos: tuple[float, float]) -> "GlobalRequest":
        """创建一个全局请求并预处理"""
        assert GlobalRequest.tokenizer is not None, "Tokenizer not initialized"
        alloc_id = GlobalRequest.allocated_id_num
        GlobalRequest.allocated_id_num += 1

        request_data["request_id"] = f"ID-{alloc_id:04d}"
        request_data["prompt"] = GlobalRequest.tokenizer.encode(request_data["prompt"])
        request_data["ignore_eos"] = True
        request_data["ctrl_original_prompt_len"] = len(request_data["prompt"])

        # 请求特定的 TPOT_SLO
        request_data["ctrl_tpot_slo"] = slos[1]

        return GlobalRequest(request_data, slos)

    def __init__(self, request_data: RequestData, slos: tuple[float, float]) -> None:
        assert GlobalRequest.config is not None, "GlobalRequest class not initialized"
        assert GlobalRequest.tokenizer is not None, "Tokenizer not initialized"

        self.request_data = request_data
        self.total_length = len(request_data["prompt"]) + request_data["max_tokens"]

        self.prefill_instance = -1  # -1 表示未分配
        self.decoding_instance = -1

        self.previous_instance = -1
        self.current_instance = -1
        self.next_instance = -1
        self.next_optimizing_instance = -1

        # 先知算法 token 数量安排
        self.prophet_ratio = 0.10
        self.prophet_len_less = int(request_data["max_tokens"] * self.prophet_ratio)
        self.prophet_len_greater = (
            request_data["max_tokens"] - self.prophet_len_less + 3
        )

        # 分离 prefill / flowing decoding 的携带信息
        self.new_token: int = 0
        self.new_chunk: bytes = b""

        # 指标
        self.request_metrics = RequestMetrics()
        self.request_metrics.record_request_id(request_data["request_id"])
        self.request_metrics.record_prefill_length(len(request_data["prompt"]))
        self.request_metrics.record_arrival_time()
        self.got_first_token: bool = False

        self.ttft_slo, self.tpot_slo = slos

    ### 属性/状态修改接口 ###

    def on_schedule_initially(
        self, prefill_instance: int, decoding_instance: int
    ) -> None:
        """初始调度时调用. 设定初始调度信息"""
        self.prefill_instance = prefill_instance
        self.decoding_instance = decoding_instance

        self.previous_instance = prefill_instance
        self.current_instance = prefill_instance
        self.next_instance = decoding_instance

        self.request_metrics.record_scheduled_time(prefill_instance)

    def on_instance_end(
        self, end_reason: str, new_token: int = 0, new_chunk: bytes = b""
    ) -> None:
        """instance 结束时调用 (含无需执行的 disaggregated prefill). 更新 instance 调度信息和携带信息"""
        self.previous_instance = self.current_instance
        if end_reason == "flowing_optimizing":
            self.current_instance = self.next_optimizing_instance
        else:
            self.current_instance = self.next_instance
        self.next_instance = -1

        self.new_token = new_token
        self.new_chunk = new_chunk

    def on_schedule_next(
        self, next_instance: int, next_optimizing_instance: int
    ) -> None:
        """调度下一个 instance 时调用. 更新 instance 调度信息"""
        # 更新 instance 调度信息
        # 对于 cp 和 pd, next_instance 为 -1, 表示不会有下一个 instance. 此刻, 他们的
        # previous_instance == prefill_instance, current_instance == decoding_instance.
        assert self.current_instance != -1
        self.next_instance = next_instance
        self.next_optimizing_instance = next_optimizing_instance

    def on_token_output(self, token: int) -> None:
        """处理输出 token 时调用. 更新 prompt"""
        self.request_data["prompt"].append(min(token, GlobalRequest.TOKEN_MAX_VALUE))
        if not self.got_first_token:
            self.request_metrics.record_first_token_time()
            self.got_first_token = True
            self.request_data["ctrl_original_first_token_time"] = (
                self.request_metrics.first_token_time
            )

    def on_request_end(self) -> None:
        """请求结束时调用. 记录 decoding 结束时间"""
        self.request_metrics.record_decoding_end_time()
        self.print_metrics_table()
        self.print_metrics_json()

    def print_metrics_table(self):
        table = PrettyTable(self.request_metrics.get_table_head())
        table.add_row(self.request_metrics.get_row_data())
        print(table)

    def print_metrics_json(self):
        """打印 JSON 格式的性能指标"""
        json_data = self.request_metrics.get_json_data()
        print(
            "[GlobalRequest] json metrics: ", json.dumps(json_data, ensure_ascii=False)
        )

    @property
    def request_id(self) -> str:
        return self.request_data["request_id"]

    @property
    def is_default_aggregated_prefill(self) -> bool:
        return self.previous_instance == self.current_instance  # for cp & hy

    ### 请求信息接口 ###

    def get_disaggregated_prefill_request(self) -> RequestData:
        assert self.next_instance != -1
        request = self.request_data.copy()
        request["max_tokens"] = 1  # 为确保仅执行 prefill
        request["ctrl_should_send_kv"] = True  # 执行完毕需要发送
        request["ctrl_send_target"] = self.next_instance  # 发送到 decoding instance
        return request

    def get_default_request(self) -> RequestData:
        assert self.new_chunk is not None
        request = self.request_data.copy()
        request["ctrl_recv_target"] = self.previous_instance
        request["ctrl_send_target"] = self.next_instance
        request["max_tokens"] = (
            self.total_length
            - len(self.request_data["prompt"])
            - (1 if len(self.new_chunk) > 0 else 0)
        )
        if self.next_optimizing_instance >= 0:
            request["ctrl_optimizing_send_target"] = self.next_optimizing_instance

        # 设定 prompt kv cache 来源 (接收 or prefill)
        if self.is_default_aggregated_prefill:  # for cp, hy, 原地 prefill 时无需发送
            request["ctrl_should_recv_kv"] = False
        else:  # for pd, hy, decoding 需要接收 kv
            request["ctrl_should_recv_kv"] = True
            request["ctrl_new_token"] = self.new_token

        # 先知算法相关配置
        if GlobalRequest.config.decoding_policy == "simple_flowing":
            request["prophet_len"] = -1
        elif GlobalRequest.config.decoding_policy == "prophet_flowing":
            if self.current_instance < self.next_instance:
                request["prophet_len"] = self.prophet_len_less
            else:
                request["prophet_len"] = self.prophet_len_greater
            if self.current_instance == 2:  # 对于混合节点, 不考虑迁移
                request["prophet_len"] = self.request_data["max_tokens"]
        else:
            raise ValueError(
                f"Unknown decoding policy: {GlobalRequest.config.decoding_policy}"
            )

        return request


class ExecutionTimePredictor:
    """根据 token 数量预测 Prefill 的执行时间
    使用最小二乘法进行线性回归
    对于长度为 1000 的数据集, 训练时间约为 0.3ms
    """

    def __init__(self, learning_window=1000):
        self.prompt_length_list: List[int] = []  # x
        self.execution_time_list: List[float] = []  # y
        self.slope, self.intercept = 0, 0  # parameters
        self.learning_window = learning_window

    def learn_new_sample(self, prompt_length: int, execution_time: float):
        self.prompt_length_list.append(prompt_length)
        self.execution_time_list.append(execution_time)

        # 约束窗口大小
        while len(self.prompt_length_list) > self.learning_window:
            self.prompt_length_list.pop(0)
            self.execution_time_list.pop(0)
            assert len(self.prompt_length_list) == len(self.execution_time_list)

        self._learn()  # 更新参数 (可替换学习算法)

    def predict_execution_time(self, num_token: int) -> float:
        return self.slope * num_token + self.intercept

    def _learn(self):
        """学习算法: 最小二乘法 全训练"""
        self.slope, self.intercept = np.polyfit(
            self.prompt_length_list, self.execution_time_list, 1
        )


class RoundRobinSelector:
    """Round Robin 选择器:轮询 [start, end)"""

    def __init__(self, start: int, end: int) -> None:
        self._num = end - start
        self._offset = start
        self._cur = 0

    def select(self) -> int:
        """选择下一个实例"""
        if self._num == 0:
            raise ValueError(
                f"Empty RoundRobinSelector, start {self._offset}, end {self._offset + self._num}"
            )
        selected = self._cur
        self._cur = (self._cur + 1) % self._num
        return selected + self._offset


class GlobalSchedulerBase(ABC):
    """全局调度器抽象类"""

    def __init__(
        self,
        config: GlobalSchedulerConfig,
    ):
        self.config = config

        self.prefill_size = self.config.world_size - self.config.decoding_size

        self._init_round_robin_data_structure()
        self._init_load_aware_data_structure()

    def _init_round_robin_data_structure(self):
        # 公共 round_robin 数据结构
        self.prefill_round_robin_selector = RoundRobinSelector(0, self.prefill_size)
        self.decoding_round_robin_selector = RoundRobinSelector(
            self.prefill_size, self.config.world_size
        )

    def _init_load_aware_data_structure(self):
        # 公共 load_aware 数据结构
        # 运行表: 某实例正在运行某 prefill 请求 (prefill instance -> request_id list)
        self.running: List[List[str]] = [[] for _ in range(self.prefill_size)]
        self.running_max_length: int = self.config.load_aware_buffer_length
        self.history_running_count: List[int] = [0 for _ in range(self.prefill_size)]
        # 等待队列: (request_ids, prompt_len, Event)
        self.waiting: List[Tuple[str, int, Event]] = []

    async def schedule(self, request: GlobalRequest) -> Tuple[int, int]:
        """调度入口, 策略由派生类实现具体调度策略"""
        if self.config.schedule_policy == "round_robin":
            return self._schedule_round_robin(request)
        elif self.config.schedule_policy == "load_aware":
            return await self._schedule_load_aware(request)
        elif self.config.schedule_policy == "length_aware":
            return self._schedule_length_aware(request)
        else:
            raise ValueError(f"Unknown schedule policy: {self.config.schedule_policy}")

    def schedule_next(self, current_instance: int) -> tuple[int, int]:
        return -1, -1  # cp, pd 随意返回, hy 由派生类实现

    @abstractmethod
    def _schedule_round_robin(self, request: GlobalRequest) -> Tuple[int, int]:
        """由派生调度器实现"""
        pass

    @abstractmethod
    async def _schedule_load_aware(self, request: GlobalRequest) -> Tuple[int, int]:
        """由派生调度器实现"""
        pass

    def _schedule_length_aware(self, request: GlobalRequest) -> Tuple[int, int]:
        return -1, -1  # cp, pd 随意返回, hy 由派生类实现

    async def _schedule_load_aware_prefill(self, request: GlobalRequest) -> int:
        """根据 load_aware 策略调度 prefill. 返回 prefill instance.
        阻塞调度函数, 直到某个 prefill instance 可用.
        "可用"被定义为: 其中 prefill 队列 < 阈值 (running_max_length).
        """
        event = self._add_waiting_in_load_aware(
            request.request_id, len(request.request_data["prompt"])
        )
        self._try_schedule_prefill_in_load_aware()
        prefill_target = await self._wait_scheduled_in_load_aware(
            request.request_id, event
        )

        return prefill_target

    def _add_waiting_in_load_aware(self, request_id: str, prompt_len: int) -> Event:
        """将请求加入等待队列"""
        event = Event()
        self.waiting.append((request_id, prompt_len, event))
        return event

    def _try_schedule_prefill_in_load_aware(self) -> None:
        """尝试调度 waiting 中的一个请求到空闲实例"""
        assert self.config.schedule_policy == "load_aware"  # 暂时仅考虑 load_aware 调度

        if len(self.waiting) == 0:  # 没有等待请求
            return
        if all(
            len(request_ids) >= self.running_max_length for request_ids in self.running
        ):
            return  # 没有空闲实例

        # 选择 running 长度最少的实例
        instance = min(
            range(self.prefill_size),
            key=lambda i: (len(self.running[i]), self.history_running_count[i]),
        )

        # 选择一个请求调度到空闲实例
        request_id, prompt_len, event = self.waiting.pop(0)
        self.running[instance].append(request_id)
        self.history_running_count[instance] += 1

        logger.debug(
            f"[Global Scheduler] load_aware: {request_id} scheduled to instance {instance}"
        )

        # 通知请求已被调度
        event.set()
        return

    async def _wait_scheduled_in_load_aware(self, request_id: str, event: Event) -> int:
        """等待请求被调度"""
        await event.wait()
        for i, request_ids in enumerate(self.running):
            if request_id in request_ids:
                return i
        else:
            raise ValueError(f"Request {request_id} not found in running list")

    def on_prefill_end(self, request: GlobalRequest):
        """Prefill 结束后的回调函数 (hybrid 单独实现)"""
        request.request_metrics.record_prefill_end_time()
        request_id = request.request_id
        if self.config.schedule_policy == "load_aware":
            for i, request_ids in enumerate(self.running):
                if request_id in request_ids:
                    self.running[i].remove(request_id)
                    break
            self._try_schedule_prefill_in_load_aware()


class ChunkedPrefillGlobalScheduler(GlobalSchedulerBase):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        assert self.config.world_size == self.prefill_size
        assert self.config.scheduler_type == "chunked_prefill"

    def _schedule_round_robin(self, request: GlobalRequest) -> Tuple[int, int]:
        prefill_target = self.prefill_round_robin_selector.select()
        return prefill_target, prefill_target # 相同 instance 标志着 aggregated prefill

    async def _schedule_load_aware(self, request: GlobalRequest) -> Tuple[int, int]:
        prefill_target = await self._schedule_load_aware_prefill(request)
        return prefill_target, prefill_target # 相同 instance 标志着 aggregated prefill


class PdDisaggregationGlobalScheduler(GlobalSchedulerBase):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        assert self.config.scheduler_type == "pd_disaggregation"
        assert self.config.decoding_size > 0

    def _schedule_round_robin(self, request: GlobalRequest) -> Tuple[int, int]:
        prefill_target = self.prefill_round_robin_selector.select()
        decoding_target = self.decoding_round_robin_selector.select()
        return prefill_target, decoding_target

    async def _schedule_load_aware(self, request: GlobalRequest) -> Tuple[int, int]:
        prefill_target = await self._schedule_load_aware_prefill(request)

        # decode 使用 round_robin 策略
        decode_target = self.decoding_round_robin_selector.select()

        return (prefill_target, decode_target)


@dataclass
class InstancePrefillState:
    instance_id: int
    running_prefills: list[GlobalRequest]
    prefill_processing_capacity: float
    first_req_start_time: float

    def remaining_tokens(self) -> int:
        """计算当前实例的剩余 token 数"""
        return sum([len(req.request_data["prompt"]) for req in self.running_prefills])

    def remaining_processing_time(self) -> float:
        """计算剩余处理时间"""
        if len(self.running_prefills) == 0:
            return 0
        # 计算总处理时间
        total_processing_time = (
            self.remaining_tokens() / self.prefill_processing_capacity
        )
        # 计算当前剩余时间
        current_time = time.time()
        remaining_time = total_processing_time - (
            current_time - self.first_req_start_time
        )
        return remaining_time

    def can_serve(self, request: GlobalRequest) -> bool:
        """判断当前实例是否可以服务请求"""
        # 请求当前排队时间
        request_queue_time = time.time() - request.request_metrics.arrival_time
        # 计算当前实例的剩余处理时间
        remaining_time = self.remaining_processing_time()
        # 计算请求的处理时间
        request_processing_time = (
            len(request.request_data["prompt"]) / self.prefill_processing_capacity
        )
        # 判断是否可以服务
        return (
            request_queue_time + remaining_time + request_processing_time
            <= request.ttft_slo
        )

    def on_add_request(self, request: GlobalRequest):
        """添加请求"""
        self.running_prefills.append(request)
        if len(self.running_prefills) == 1:
            self.first_req_start_time = time.time()

    def on_remove_request(self, request: GlobalRequest):
        """移除请求"""
        self.running_prefills.remove(request)
        self.first_req_start_time = time.time()


class HybridGlobalScheduler(GlobalSchedulerBase):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        assert self.config.scheduler_type == "hybrid"

        prefix_group_size_sum = lambda i: sum(self.config.group_sizes[:i])

        # 分组 flowing decoding 数据结构
        self.flowing_in_group_selectors = [
            RoundRobinSelector(
                prefix_group_size_sum(i),
                prefix_group_size_sum(i) + group_size,
            )
            for i, group_size in enumerate(self.config.group_sizes)
        ]
        self.optimizing_group_selector = deepcopy(self.flowing_in_group_selectors[-1])

        if self.config.enable_zero_decoding:
            self.prefill_size = self.config.world_size
            self._init_round_robin_data_structure()
            self._init_load_aware_data_structure()

        # length_aware 数据结构
        assert (
            len(self.config.prefill_processing_capacities) >= self.prefill_size
        ), f"Prefill processing capacities {self.config.prefill_processing_capacities} should be at least {self.prefill_size}"
        self.prefill_instances = [
            InstancePrefillState(
                i,
                [],
                self.config.prefill_processing_capacities[i],
                0,
            )
            for i in range(self.prefill_size)
        ]

    def _schedule_round_robin(self, request: GlobalRequest) -> Tuple[int, int]:
        prefill_target = self.prefill_round_robin_selector.select()
        if self.config.decoding_policy == "simple_flowing":
            decoding_target = self.decoding_round_robin_selector.select()
        elif self.config.decoding_policy == "prophet_flowing":
            decoding_target = prefill_target
        else:
            raise ValueError(f"Unknown decoding policy: {self.config.decoding_policy}")
        return prefill_target, decoding_target

    async def _schedule_load_aware(self, request: GlobalRequest) -> Tuple[int, int]:
        prefill_target = await self._schedule_load_aware_prefill(request)
        if self.config.decoding_policy == "simple_flowing":
            if (
                self.config.enable_zero_decoding
                and self._get_current_group(prefill_target)
                == len(self.flowing_in_group_selectors) - 1
            ):
                # 如果允许 zero_decoding, 则 Prefill 选择最后一个组
                decoding_target = prefill_target
            else:
                decoding_target = self.flowing_in_group_selectors[-1].select()
        elif self.config.decoding_policy == "prophet_flowing":
            decoding_target = prefill_target
        else:
            raise ValueError(f"Unknown decoding policy: {self.config.decoding_policy}")
        return prefill_target, decoding_target

    def _schedule_length_aware(self, request: GlobalRequest) -> Tuple[int, int]:

        feasible_instances = [
            i
            for i in range(self.prefill_size)
            if self.prefill_instances[i].can_serve(request)
        ]
        logger.debug(
            f"[Global Scheduler] feasible_instances: {feasible_instances} for {request.request_id} {len(request.request_data['prompt'])} tokens"
        )
        if len(feasible_instances) == 0:
            logger.debug(
                f"[Global Scheduler] remaining tokens {[self.prefill_instances[i].remaining_tokens() for i in range(self.prefill_size)]}"
            )
            logger.debug(
                f"[Global Scheduler] remaining processing time {[self.prefill_instances[i].remaining_processing_time() for i in range(self.prefill_size)]}"
            )

        # 选择 prefill 实例
        prefill_instance = min(
            feasible_instances if feasible_instances else range(self.prefill_size),
            key=lambda i: self.prefill_instances[i].remaining_tokens(),
        )

        self.prefill_instances[prefill_instance].on_add_request(request)

        logger.debug(
            f"[Global Scheduler] remaining tokens {[self.prefill_instances[i].remaining_tokens() for i in range(self.prefill_size)]}"
        )

        # 确定 decoding 实例
        if (
            self.config.enable_zero_decoding
            and self._get_current_group(prefill_instance)
            == len(self.flowing_in_group_selectors) - 1
        ):
            # 如果允许 zero_decoding, 则 Prefill 选择最后一个组
            decoding_instance = prefill_instance
        else:
            decoding_instance = self.flowing_in_group_selectors[-1].select()

        return prefill_instance, decoding_instance

    def schedule_next(self, current_instance: int) -> tuple[int, int]:
        if self.config.decoding_policy == "simple_flowing":
            return self._schedule_next_simple_flowing(current_instance)
        elif self.config.decoding_policy == "prophet_flowing":
            return self._schedule_next_prophet_flowing(current_instance)
        else:
            raise ValueError(f"Unknown decoding policy: {self.config.decoding_policy}")

    def _get_current_group(self, current_instance: int) -> int:
        prefix_group_size_sum = lambda i: sum(self.config.group_sizes[:i])
        prefix_group_size = [
            prefix_group_size_sum(i + 1) for i in range(len(self.config.group_sizes))
        ]
        current_group = -1
        for i, prefix_upper_bound in enumerate(prefix_group_size):
            if current_instance < prefix_upper_bound:
                current_group = i
                break
        assert (
            current_group != -1
        ), f"Invalid current_instance {current_instance} for group sizes {self.config.group_sizes}"
        return current_group

    def _schedule_next_simple_flowing(self, current_instance: int) -> tuple[int, int]:
        # 计算当前实例所在的组
        current_group = self._get_current_group(current_instance)

        # 在下一个劣化组中选择劣化实例
        next_degrading_group = (current_group - 1) % len(self.config.group_sizes)
        next_degrading_instance = self.flowing_in_group_selectors[
            next_degrading_group
        ].select()
        # 选择优化实例, 如果当前不在优化组中
        if current_group < len(self.config.group_sizes) - 1:
            next_optimizing_instance = self.optimizing_group_selector.select()
        else:
            next_optimizing_instance = -1
        return next_degrading_instance, next_optimizing_instance

    def _schedule_next_prophet_flowing(self, current_instance: int) -> int:
        # 在区间 [self.config.world_size-self.config.decoding_size, self.config.world_size) 中选择 current_instance 的轴对称值
        assert False, "Prophet Flowing Decoding not implemented yet"
        if self.config.world_size == 4:
            if current_instance == 2:
                return 2
            elif current_instance == 3:
                return 1
            else:
                return 3
        else:
            assert self.config.world_size == 2
            return 1 - current_instance

    def _try_schedule_prefill_in_load_aware(self) -> None:
        """尝试调度 waiting 中的一个请求到空闲实例"""
        assert self.config.schedule_policy == "load_aware"  # 暂时仅考虑 load_aware 调度

        if len(self.waiting) == 0:  # 没有等待请求
            return
        if all(
            len(request_ids) >= self.running_max_length for request_ids in self.running
        ):
            return  # 没有空闲实例

        for i, (request_id, prompt_len, event) in enumerate(self.waiting):

            # 可用的 prefill instance
            available_prefill_instances = [
                i
                for i in range(self.prefill_size)
                if len(self.running[i]) < self.running_max_length
                and prompt_len <= self.config.prefill_token_limit[i]
            ]

            if len(available_prefill_instances) == 0:
                continue

            # 选择 running 长度最少的实例 (最低负载)
            instance = min(
                available_prefill_instances,
                key=lambda i: (len(self.running[i]), self.history_running_count[i]),
            )

            # 选择一个请求调度到空闲实例
            self.waiting.pop(i)
            self.running[instance].append(request_id)
            self.history_running_count[instance] += 1

            logger.debug(
                f"[Global Scheduler] load_aware: {request_id} scheduled to instance {instance}"
            )

            # 通知请求已被调度
            event.set()
            return

    def on_prefill_end(self, request: GlobalRequest):
        """Prefill 结束后的回调函数"""
        request.request_metrics.record_prefill_end_time()
        request_id = request.request_id
        if self.config.schedule_policy == "load_aware":
            for i, request_ids in enumerate(self.running):
                if request_id in request_ids:
                    self.running[i].remove(request_id)
                    break
            self._try_schedule_prefill_in_load_aware()
        elif self.config.schedule_policy == "length_aware":
            self.prefill_instances[request.prefill_instance].on_remove_request(request)


def global_scheduler_class_selector(scheduler_type: str):
    if scheduler_type == "chunked_prefill":
        return ChunkedPrefillGlobalScheduler
    elif scheduler_type == "pd_disaggregation":
        return PdDisaggregationGlobalScheduler
    elif scheduler_type == "hybrid":
        return HybridGlobalScheduler
    else:
        raise ValueError(f"Unknown scheduler type: {scheduler_type}")
