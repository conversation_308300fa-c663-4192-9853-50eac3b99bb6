import logging.config
import os

import aiohttp
import bisect
from quart import Quart, make_response, request
import argparse
import logging
from typing import List, Dict, Tuple, Any, Optional, AsyncGenerator
import json
import traceback
import uuid
import math
from sklearn.linear_model import SGDRegressor
import numpy as np
import time

assert False, "Deprecated code, use vllm/distributed/global_scheduler.py instead"


_LOG_LEVEL = os.getenv("VLLM_LOGGING_LEVEL", "INFO")
_FORMAT = f"[%(levelname)s %(asctime)s " "%(filename)s:%(lineno)d] %(message)s"
_DATE_FORMAT = "%m-%d %H:%M:%S"
logging.basicConfig(level=_LOG_LEVEL, format=_FORMAT, datefmt=_DATE_FORMAT)

logger = logging.getLogger("proxy")


class ExecutionTimePredictor:
    """根据 token 数量预测 Prefill 的执行时间
    使用最小二乘法进行线性回归
    对于长度为 1000 的数据集, 训练时间约为 0.3ms
    """

    def __init__(self, learning_window=1000):
        self.prompt_length_list: List[int] = []  # x
        self.execution_time_list: List[float] = []  # y
        self.slope, self.intercept = 0, 0  # parameters
        self.learning_window = learning_window

    def learn_new_sample(self, prompt_length: int, execution_time: float):
        self.prompt_length_list.append(prompt_length)
        self.execution_time_list.append(execution_time)

        # 约束窗口大小
        while len(self.prompt_length_list) > self.learning_window:
            self.prompt_length_list.pop(0)
            self.execution_time_list.pop(0)
            assert len(self.prompt_length_list) == len(self.execution_time_list)

        self._learn()  # 更新参数 (可替换学习算法)

    def predict_execution_time(self, num_token: int) -> float:
        return self.slope * num_token + self.intercept

    def _learn(self):
        """学习算法: 最小二乘法 全训练"""
        self.slope, self.intercept = np.polyfit(
            self.prompt_length_list, self.execution_time_list, 1
        )


class GlobalScheduler:

    def __init__(self, world_size: int, decode_size: int):
        assert 0 <= decode_size <= world_size
        self.world_size = world_size
        self.decode_size = decode_size
        self.prefill_size = self.world_size - decode_size
        self.prefill_count = 0
        self.decode_count = 0
        self.id_count = 0

        # 记录历史 Prefill 长度, 为了 prefill 长度感知
        self.prefill_length_history: List[int] = []
        # 当前正在 Prefill 的队列 (实例号 -> (请求ID, 长度)列表), 为了 prefill 负载感知
        self.scheduled_prefills: List[List[Tuple[str, int]]] = [
            [] for _ in range(self.prefill_size)
        ]
        # 时间预测器 (实例号->预测器), 为了 prefill 负载预测
        self.predictors = [ExecutionTimePredictor() for _ in range(self.prefill_size)]

    def get_new_request_id(self) -> str:
        self.id_count += 1
        return f"ID-{self.id_count}-uuid-{uuid.uuid4()}"

    def schedule_new_length_aware(
        self, request_json: Dict[str, Any]
    ) -> Tuple[int, int]:
        """hybrid 的新请求调度方案
        返回 (prefill instance, decode instance)
        """
        prefill_target = self._schedule_length_aware_prefill(request_json)
        decode_target = self.world_size - 1

        return prefill_target, decode_target

    def _learn_execution_time(
        self, prefill_instance: int, prompt_length: int, execution_time: float
    ):
        self.predictors[prefill_instance].learn_new_sample(
            prompt_length, execution_time
        )
        predicted_time = self.predictors[prefill_instance].predict_execution_time(
            prompt_length
        )
        error = abs(predicted_time - execution_time)
        if error > 0.01:
            logger.debug(
                f"Prediction loss for instance {prefill_instance}: {error:.4f} seconds"
            )

    def _schedule_length_aware_prefill(self, request_json: Dict[str, Any]) -> int:
        """hybrid 的 length-aware prefill 调度
        返回 Prefill instance
        """
        request_id = request_json["request_id"]
        length = len(request_json["prompt"])
        idx = bisect.bisect_right(self.prefill_length_history, length)
        self.prefill_length_history.insert(idx, length)

        reverse_idx = len(self.prefill_length_history) - idx - 1
        percentile = reverse_idx / len(self.prefill_length_history)
        prefill_target = math.floor(percentile * self.prefill_size)

        # 负载感知 (暂时: 空闲实例, 待实现: 考虑排队时间/已执行时间)
        if len(self.scheduled_prefills[prefill_target]) > 0:
            for i in range(self.prefill_size):
                # 寻找最近的空闲实例
                is_rescheduled = False
                for candidate in [prefill_target + i, prefill_target - i]:
                    candidate = candidate % self.prefill_size
                    if len(self.scheduled_prefills[candidate]) == 0:
                        is_rescheduled = True
                        prefill_target = candidate
                        break
                if is_rescheduled:
                    break

        self.scheduled_prefills[prefill_target].append((request_id, length))
        return prefill_target

    def on_end_prefill(
        self, request_json: Dict[str, Any], prefill_instance: int, execution_time: float
    ):
        """结束 Prefill"""
        request_id = request_json["request_id"]
        prompt_length = len(request_json["prompt"])

        # 维护"正在运行的Prefill"
        for i, (rid, _) in enumerate(self.scheduled_prefills[prefill_instance]):
            if rid == request_id:
                self.scheduled_prefills[prefill_instance].pop(i)
                break

        self._learn_execution_time(prefill_instance, prompt_length, execution_time)

    def schedule_next_flowing_decoding(self, current_instance: int) -> int:
        """hybrid 的 flowing decoding 调度: 决定 decoding 的下一跳"""
        return (current_instance - 1) % self.world_size

    def schedule_new_hybrid_round_robin(
        self, request_data: Dict[str, Any]
    ) -> Tuple[int, int]:
        """round-robin 的 Prefill 和 Decode 调度
        返回 (prefill instance, decode instance)
        """
        prefill_target = self.prefill_count
        self.prefill_count = (self.prefill_count + 1) % self.prefill_size

        decode_target = self.world_size - 1

        return prefill_target, decode_target

    def schedule_new_pd_round_robin(
        self, request_data: Dict[str, Any]
    ) -> Tuple[int, int]:
        """round-robin 的 Prefill 和 Decode 调度
        返回 (prefill instance, decode instance)
        """
        prefill_target = self.prefill_count
        self.prefill_count = (self.prefill_count + 1) % self.prefill_size

        decode_target = self.decode_count + self.prefill_size
        self.decode_count = (self.decode_count + 1) % self.decode_size

        return prefill_target, decode_target

    def schedule_new_cp_round_robin(
        self, request_data: Dict[str, Any]
    ) -> Tuple[int, int]:
        """round-robin 的 chunked prefill 调度
        返回 (prefill instance, decode instance) 兼容的接口
        """
        assert self.prefill_size == self.world_size
        prefill_target = self.prefill_count
        self.prefill_count = (self.prefill_count + 1) % self.prefill_size

        # decode_target = self.decode_count
        # self.decode_count = (self.decode_count + 1) % self.decode_size

        return prefill_target, 0
