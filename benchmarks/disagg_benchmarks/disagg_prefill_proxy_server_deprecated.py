import logging.config
import os

import aiohttp
import bisect
from quart import Quart, make_response, request
import argparse
import logging
from typing import List, Dict, Tuple, Any, Optional, AsyncGenerator
import json
import traceback
import uuid
import math
from sklearn.linear_model import SGDRegressor
import numpy as np
import time


from transformers import PreTrainedTokenizerBase, AutoTokenizer
from .global_scheduler_deprecated import GlobalScheduler

assert (
    False
), "Deprecated code, use vllm/entrypoints/openai/distributed_proxy.py instead"


tokenizer: Optional[PreTrainedTokenizerBase] = None


_LOG_LEVEL = os.getenv("VLLM_LOGGING_LEVEL", "INFO")
_FORMAT = f"[%(levelname)s %(asctime)s " "%(filename)s:%(lineno)d] %(message)s"
_DATE_FORMAT = "%m-%d %H:%M:%S"
logging.basicConfig(level=_LOG_LEVEL, format=_FORMAT, datefmt=_DATE_FORMAT)

logger = logging.getLogger("proxy")

AIOHTTP_TIMEOUT = aiohttp.ClientTimeout(total=6 * 60 * 60)

app = Quart(__name__)

INSTANCE_PORTS: List[int] = []
WORLD_SIZE: int = 0
DECODE_SIZE: int = 0
SCHEDULER_TYPE: str = ""


global_scheduler: Optional[GlobalScheduler] = None


async def forward_simple_request(url, data) -> AsyncGenerator[bytes, None]:
    async with aiohttp.ClientSession(timeout=AIOHTTP_TIMEOUT) as session:
        headers = {"Authorization": f"Bearer {os.environ.get('OPENAI_API_KEY')}"}
        async with session.post(url=url, json=data, headers=headers) as response:
            if response.status == 200:
                async for chunk_bytes in response.content:
                    yield chunk_bytes
            else:
                logger.error(f"Error occurred: {response.status}")
                raise Exception(f"Error occurred: {response.status}")


async def forward_completion_request(url, data) -> AsyncGenerator[bytes, None]:
    async with aiohttp.ClientSession(timeout=AIOHTTP_TIMEOUT) as session:
        headers = {
            "Authorization": f"Bearer {os.environ.get('OPENAI_API_KEY', 'None')}"
        }
        buffer = ""
        async with session.post(url=url, json=data, headers=headers) as response:
            if response.status == 200:
                async for chunk_bytes in response.content:
                    chunk = chunk_bytes.decode("utf-8")
                    buffer += chunk
                    while "\n\n" in buffer:
                        response_list = buffer.split("\n\n")
                        first_response = response_list.pop(0)
                        buffer = "\n\n".join(response_list)
                        yield (first_response + "\n\n").encode("utf-8")
            else:
                logger.error(f"Error occurred: {response.status}")
                raise Exception(f"Error occurred: {response.status}")


def remove_prefix(text: str, prefix: str) -> str:
    return text[len(prefix) :] if text.startswith(prefix) else text


def get_field_from_chunk(chunk: bytes, field: str) -> Any:
    try:
        data = remove_prefix(chunk.decode("utf-8"), "data:")

        if field == "DONE":
            return "[DONE]" in data

        response = json.loads(data)

        if field == "finish_reason":
            # decode response 返回示例:
            # {"id":"cmpl-7ade7f4af2dd45eab5a721793ade30c8","object":"text_completion","created":1734340719,
            # "model":"meta-llama/Meta-Llama-3.1-8B","choices":[{"index":0,"text":" scenic","token_ids":[62081],
            # "logprobs":null,"finish_reason":"length","stop_reason":null}],"usage":null}
            return response["choices"][0]["finish_reason"]  # -> str
        elif field == "delta_tokens":
            # 架构同上
            return response["choices"][0]["token_ids"]  # -> List[int]
        elif field == "text":
            return response["choices"][0]["text"]  # -> str
        elif field == "prompt_tokens":
            # tokenize 返回示例:
            # {"count":5,"max_model_len":2048,"tokens":[128000,24661,13175,374,264]}
            return response["tokens"]  # -> List[int]
        else:
            raise ValueError(f"Unknown field: {field}")
    except Exception as e:
        logger.error(
            f"Error occurred when parsing chunk: {e} in {chunk.decode('utf-8')}"
        )
        return False


async def handle_flowing_decode(
    original_request_data: Dict["str", Any],
    prefill_instance: int,
    decode_instance: int,
    token_ids: List[int],
) -> AsyncGenerator[bytes, None]:
    """处理混合方案的 Decode
    如果 LS 不抛出流动请求, 则退化为普通的 PD 分离
    """
    global global_scheduler
    assert global_scheduler is not None

    if "max_tokens" in original_request_data:
        max_total_tokens = original_request_data["max_tokens"] + len(token_ids)
        original_request_data["ignore_eos"] = True
    else:
        assert False, "max_tokens not found in original_request_data"
        original_request_data["ignore_eos"] = False

    prev_instance = prefill_instance
    current_instance = decode_instance

    need_continuing = True
    finished = False
    while need_continuing and not finished:
        need_continuing = False
        try:
            next_instance = global_scheduler.schedule_next_flowing_decoding(
                current_instance
            )

            decode_request = original_request_data.copy()
            # prepare the request data for decode
            decode_request["prompt"] = token_ids
            decode_request["need_recv_kv"] = True
            decode_request["recv_target"] = prev_instance
            decode_request["send_target"] = next_instance
            decode_request["max_tokens"] = max_total_tokens - len(token_ids)

            generator = forward_completion_request(
                f"http://localhost:{INSTANCE_PORTS[current_instance]}/v1/completions",
                decode_request,
            )

            async for chunk in generator:
                if get_field_from_chunk(chunk, "DONE"):
                    if finished:  # 返回最后一个 [Done], 忽略其他的 [Done]
                        logger.debug("[Proxy] Finish [Done]")
                        yield chunk
                    else:
                        logger.critical(
                            "[CRITICAL] >>> Flowing [Done] of request {original_request_data['request_id']}"
                        )
                else:
                    finish_reason = get_field_from_chunk(chunk, "finish_reason")
                    if finish_reason == "flowing":  # 检测到因为流动的停止
                        # logger.critical(f"[CRITICAL] Finish reason: {finish_reason}")
                        need_continuing = True
                        # 忽略输出
                    else:
                        if finish_reason == "length":  # 检测到因为长度的停止
                            finished = True
                        if delta_tokens := get_field_from_chunk(chunk, "delta_tokens"):
                            token_ids += delta_tokens
                        yield chunk

            # for next round
            prev_instance = current_instance
            current_instance = next_instance

        except Exception as e:
            logger.error(f"Error occurred during decode: {e}")
            logger.error(traceback.format_exc())


async def handle_hybrid_request():
    global global_scheduler, tokenizer
    assert global_scheduler is not None
    assert tokenizer is not None

    try:
        original_request_data = await request.get_json()

        # 核心: 全局 ID 生成
        request_id = global_scheduler.get_new_request_id()
        original_request_data["request_id"] = request_id

        token_ids: List[int] = tokenizer.encode(original_request_data["prompt"])
        original_request_data["prompt"] = token_ids
        logger.debug(f"[Proxy] Tokenize Done: {request_id} has {len(token_ids)} tokens")

        # 首次调度
        prefill_instance, decode_instance = (
            global_scheduler.schedule_new_hybrid_round_robin(original_request_data)
        )
        logger.debug(
            f"Request {request_id}: prefill port: {INSTANCE_PORTS[prefill_instance]}, send port: {INSTANCE_PORTS[decode_instance]}"
        )

        # 准备 prefill 请求
        prefill_request = original_request_data.copy()
        # change max_tokens = 1 to let it only do prefill
        prefill_request["max_tokens"] = 1
        prefill_request["need_send_kv"] = True
        prefill_request["send_target"] = decode_instance

        # 发送给 Prefill instance 并计时
        # TODO: 考虑仅完成 Prefill 时才进行下一个请求的发送, 以确保准确记录 Prefill 执行时间
        start_time = time.time()
        async for chunk in forward_completion_request(
            f"http://localhost:{INSTANCE_PORTS[prefill_instance]}/v1/completions",
            prefill_request,
        ):
            # print(chunk.decode("utf-8"))
            pass
        end_time = time.time()

        execution_time = end_time - start_time
        global_scheduler.on_end_prefill(
            original_request_data, prefill_instance, execution_time
        )

        # 调用 flowing decoding
        generator = handle_flowing_decode(
            original_request_data, prefill_instance, decode_instance, token_ids
        )
        assert generator is not None, "Generator is None"

        response = await make_response(generator)
        response.timeout = None
        return response

    except Exception as e:
        import sys
        import traceback
        import argparse

        exc_info = sys.exc_info()
        print("Error occurred in disagg prefill proxy server")
        print(e)
        print("".join(traceback.format_exception(*exc_info)))
        exit(1)


async def handle_pd_request():
    global global_scheduler
    assert global_scheduler is not None
    try:
        original_request_data = await request.get_json()

        # 核心: 全局 ID 生成
        request_id = global_scheduler.get_new_request_id()
        original_request_data["request_id"] = request_id

        # 为了方便计算 Prefill 长度和维护 flowing decoding 的 tokens,
        # 使用 token IDs 代替文本 prompt. 默认调用 instance0, 后期考虑让 proxy 执行
        token_ids: List[int] = []
        async for chunk in forward_simple_request(
            f"http://localhost:{INSTANCE_PORTS[0]}/tokenize",
            {
                "model": original_request_data["model"],
                "prompt": original_request_data["prompt"],
            },
        ):
            if prompt_tokens := get_field_from_chunk(chunk, "prompt_tokens"):
                token_ids += prompt_tokens

        assert len(token_ids) > 0
        original_request_data["prompt"] = token_ids
        logger.debug(f"[Proxy] Tokenize Done: {request_id} has {len(token_ids)} tokens")

        # 首次调度
        prefill_instance, decode_instance = (
            global_scheduler.schedule_new_pd_round_robin(original_request_data)
        )
        logger.debug(
            f"Request {request_id}: prefill port: {INSTANCE_PORTS[prefill_instance]}, decode port: {INSTANCE_PORTS[decode_instance]}"
        )

        # 准备 prefill 请求
        prefill_request = original_request_data.copy()
        # change max_tokens = 1 to let it only do prefill
        prefill_request["max_tokens"] = 1
        prefill_request["need_send_kv"] = True
        prefill_request["send_target"] = decode_instance

        # 发送给 Prefill instance 并计时
        async for chunk in forward_completion_request(
            f"http://localhost:{INSTANCE_PORTS[prefill_instance]}/v1/completions",
            prefill_request,
        ):
            # print(chunk.decode("utf-8"))
            pass

        # 准备 decode 请求
        decode_request = original_request_data.copy()
        decode_request["need_recv_kv"] = True
        decode_request["recv_target"] = prefill_instance
        decode_request["send_target"] = 0  # 不会触发 (由 config 不启动 flowing)

        # return decode
        generator = forward_simple_request(
            f"http://localhost:{INSTANCE_PORTS[decode_instance]}/v1/completions",
            decode_request,
        )
        assert generator is not None, "Generator is None"
        response = await make_response(generator)
        response.timeout = None

        return response

    except Exception as e:
        import sys
        import traceback
        import argparse

        exc_info = sys.exc_info()
        print("Error occurred in disagg prefill proxy server")
        print(e)
        print("".join(traceback.format_exception(*exc_info)))
        exit(1)


async def handle_cp_request():
    global global_scheduler
    assert global_scheduler is not None
    try:
        original_request_data = await request.get_json()

        # 核心: 全局 ID 生成
        request_id = global_scheduler.get_new_request_id()
        original_request_data["request_id"] = request_id

        # 为了方便计算 Prefill 长度和维护 flowing decoding 的 tokens,
        # 使用 token IDs 代替文本 prompt. 默认调用 instance0, 后期考虑让 proxy 执行
        token_ids: List[int] = []
        async for chunk in forward_simple_request(
            f"http://localhost:{INSTANCE_PORTS[0]}/tokenize",
            {
                "model": original_request_data["model"],
                "prompt": original_request_data["prompt"],
            },
        ):
            if prompt_tokens := get_field_from_chunk(chunk, "prompt_tokens"):
                token_ids += prompt_tokens

        assert len(token_ids) > 0
        original_request_data["prompt"] = token_ids
        logger.debug(f"[Proxy] Tokenize Done: {request_id} has {len(token_ids)} tokens")

        # 首次调度
        prefill_instance, decode_instance = (
            global_scheduler.schedule_new_cp_round_robin(original_request_data)
        )
        logger.debug(
            f"Request {request_id}: prefill port: {INSTANCE_PORTS[prefill_instance]}, send port: {INSTANCE_PORTS[decode_instance]}"
        )

        generator = forward_simple_request(
            f"http://localhost:{INSTANCE_PORTS[prefill_instance]}/v1/completions",
            original_request_data,
        )
        assert generator is not None, "Generator is None"
        response = await make_response(generator)
        response.timeout = None

        return response

    except Exception as e:
        import sys
        import traceback
        import argparse

        exc_info = sys.exc_info()
        print("Error occurred in disagg prefill proxy server")
        print(e)
        print("".join(traceback.format_exception(*exc_info)))
        exit(1)


@app.route("/v1/completions", methods=["POST"])
async def handle_request():
    if SCHEDULER_TYPE == "hybrid":
        return await handle_hybrid_request()
    elif SCHEDULER_TYPE == "pd":
        return await handle_pd_request()
    elif SCHEDULER_TYPE == "cp":
        return await handle_cp_request()
    else:
        raise ValueError(f"Unknown schedule type: {SCHEDULER_TYPE}")


def main():
    global global_scheduler, tokenizer, INSTANCE_PORTS, WORLD_SIZE, SCHEDULER_TYPE, PREFILL_SIZE
    parser = argparse.ArgumentParser(description="Disagg Prefill Proxy Server")
    parser.add_argument('--world_size', type=int, required=True, help='World size for the proxy server')
    parser.add_argument(
        "--instance_ports",
        type=int,
        nargs="+",
        required=True,
        help="Ports for the instances",
    )
    parser.add_argument(
        "--proxy_port", type=int, required=True, help="Port for the proxy server"
    )
    parser.add_argument(
        "--scheduler_type",
        type=str,
        choices=["cp", "pd", "hybrid"],
        required=True,
    )
    parser.add_argument(
        "--decode_size",
        type=int,
        required=True,
        help="Prefill size for the proxy server",
    )
    parser.add_argument(
        "--model",
        type=str,
        required=True,
        help="Model for the tokenizer in the proxy server",
    )

    args = parser.parse_args()

    WORLD_SIZE = args.world_size
    DECODE_SIZE = args.decode_size
    SCHEDULER_TYPE = args.scheduler_type
    INSTANCE_PORTS = args.instance_ports
    logger.info(
        f"World size: {WORLD_SIZE}, decode size: {DECODE_SIZE}, instance ports: {INSTANCE_PORTS}"
    )
    if SCHEDULER_TYPE == "hybrid":
        logger.info(f"[SCHEDULER TYPE] HYBRID")
    elif SCHEDULER_TYPE == "cp":
        logger.info(f"[SCHEDULER TYPE] CHUNKED PREFILL")
    elif SCHEDULER_TYPE == "pd":
        logger.info(f"[SCHEDULER TYPE] PD DISAGGREGATION")
    else:
        raise ValueError(f"Unknown schedule type: {SCHEDULER_TYPE}")
    assert len(INSTANCE_PORTS) == WORLD_SIZE and WORLD_SIZE > 0

    global_scheduler = GlobalScheduler(WORLD_SIZE, DECODE_SIZE)
    tokenizer = AutoTokenizer.from_pretrained(args.model)

    app.run(port=args.proxy_port)


if __name__ == "__main__":
    main()
