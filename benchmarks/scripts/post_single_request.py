import requests
import json
import asyncio
import aiohttp
import os
import time
from typing import AsyncGenerator

AIOHTTP_TIMEOUT = aiohttp.ClientTimeout(total=10000000)


async def iter_response(
    content: AsyncGenerator[bytes, None]
) -> AsyncGenerator[str, None]:
    buffer = ""
    async for chunk in content:
        buffer += chunk.decode("utf-8")
        if "\n\n" in buffer:
            data, buffer = buffer.split("\n\n", 1)
            yield data


async def fetch_stream():
    async with aiohttp.ClientSession(timeout=AIOHTTP_TIMEOUT) as session:
        url = "http://localhost:14560/v1/completions"

        prompt = "San Francisco is a"
        # prompt = [100+i for i in range(1000)]

        payload = {
            # "model": "meta-llama/Meta-Llama-3-8B",
            "model": "facebook/opt-125m",
            "prompt": prompt,
            "temperature": 0.0,
            "max_tokens": 50,
            "stream": True,
            "ignore_eos": True,
        }
        headers = {
            "Authorization": f"Bearer {os.environ.get('OPENAI_API_KEY')}"
        }

        try:
            async with session.post(url, headers=headers, json=payload) as response:
                response.raise_for_status()
                text_buffer = []
                async for data in iter_response(response.content):
                    data = data.strip()
                    if not data:
                        continue
                    data = data.removeprefix("data: ")
                    print(data)
                    if data != "[DONE]":
                        data = json.loads(data)
                        text_buffer.append(data["choices"][0]["text"])
                    print(f"Completion results: {"".join(text_buffer)}")
        except json.JSONDecodeError as e:
            print(f"Failed to decode JSON: {e}")
        except Exception as e:
            print(f"Request failed: {e}")

asyncio.run(fetch_stream())
