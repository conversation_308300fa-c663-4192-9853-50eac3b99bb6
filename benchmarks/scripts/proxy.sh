#!/bin/bash
# 载入配置
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd); 
source "$SCRIPT_DIR/config.sh"

# 命令行参数
WORLD_SIZE=${1:-2} # cli param 1, to set num_instances, default is 2

# 提取配置
PREFILL_PORTS=${PREFILL_PORTS[@]}
DECODE_PORTS=(${DECODE_PORTS[@]:0:$(($WORLD_SIZE - 1))}) # [0: $WORLD_SIZE - 1) 

$PYTHON_PATH -m benchmarks.disagg_benchmarks.disagg_prefill_proxy_server \
  --proxy_port $PROXY_PORT \
  --world_size $WORLD_SIZE \
  --prefill_ports ${PREFILL_PORTS[@]} \
  --decode_ports ${DECODE_PORTS[@]}