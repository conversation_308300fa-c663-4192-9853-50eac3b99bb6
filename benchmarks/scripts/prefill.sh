#!/bin/bash
# 载入配置
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd); 
source "$SCRIPT_DIR/config.sh"

# 命令行参数
WORLD_SIZE=${1:-2} # cli param 1, to set num_instances, default is 2

# 提取配置
PREFILL_DEVICE=${PREFILL_DEVICES[0]}
PREFILL_PORT=${PREFILL_PORTS[0]}
PREFILL_RANK=${PREFILL_RANKS[0]}

HF_HUB_OFFLINE=1 CUDA_VISIBLE_DEVICES=$PREFILL_DEVICE $PYTHON_PATH \
  -m vllm.entrypoints.openai.api_server \
  --model $DEFAULT_MODEL_NAME \
  --port $PREFILL_PORT \
  --max_model_len 2048 \
  --enable-chunked-prefill \
  --gpu-memory-utilization 0.8 \
  --kv-transfer-config \
    '{"kv_connector":"PyNcclConnector","kv_role":"kv_producer","kv_rank":'"$PREFILL_RANK"',"kv_parallel_size":'"$WORLD_SIZE"',"kv_buffer_size":2e9}'