#!/bin/bash
# 载入配置
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd); 
source "$SCRIPT_DIR/config.sh"


# 命令行参数
WORLD_SIZE=${1:-2} # cli param 1, to set num_instances, default is 2
DECODE_NO=${2:-0} # cli param 2, to set decode_no, default is 0


# 提取配置
DECODE_DEVICE=${DECODE_DEVICES[$DECODE_NO]}
DECODE_PORT=${DECODE_PORTS[$DECODE_NO]}
DECODE_RANK=${DECODE_RANKS[$DECODE_NO]}

HF_HUB_OFFLINE=1 CUDA_VISIBLE_DEVICES=$DECODE_DEVICE $PYTHON_PATH \
  -m vllm.entrypoints.openai.api_server \
  --model $DEFAULT_MODEL_NAME \
  --port $DECODE_PORT \
  --max_model_len 2048 \
  --enable-chunked-prefill \
  --gpu-memory-utilization 0.8 \
  --kv-transfer-config \
    '{"kv_connector":"PyNcclConnector","kv_role":"kv_consumer","kv_rank":'"$DECODE_RANK"',"kv_parallel_size":'"$WORLD_SIZE"',"kv_buffer_size":2e9}'