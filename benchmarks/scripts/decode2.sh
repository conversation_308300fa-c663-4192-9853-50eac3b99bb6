#!/bin/bash
# 载入配置
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd); 
source "$SCRIPT_DIR/config.sh"

HF_HUB_OFFLINE=1 CUDA_VISIBLE_DEVICES=$DECODE_DEVICE2 $PYTHON_PATH \
  -m vllm.entrypoints.openai.api_server \
  --model $DEFAULT_MODEL_NAME \
  --port $DECODE_PORT2 \
  --max_model_len 2048 \
  --enable-chunked-prefill \
  --gpu-memory-utilization 0.8 \
  --kv-transfer-config \
    '{"kv_connector":"PyNcclConnector","kv_role":"kv_consumer","kv_rank":2,"kv_parallel_size":3,"kv_buffer_size":2e9}'