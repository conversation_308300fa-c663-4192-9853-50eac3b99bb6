### 配置数据集负载 ###
# 32B, 4 卡 A800, ShareGPT
selected_dataset: 2 

qps_list: [14, 16, 18, 20, 22, 24, 26, 28, 30, 32]
num_prompts: 512

### 配置指标 ###
steady_state_start: 0
steady_state_end: 1
ttft_slo: 3
tpot_slo: 0.070

slo_scale_qps: 28
slo_scale_list: [4, 3.5, 3, 2.5, 2, 1.5, 1.0, 0.9]

### 配置模型 ###
selected_model: 2
world_size: 4
tp_size: 2

watermark: 0.05
load_aware_buffer_length: 4

### 配置测试对象 ###
schedulers:
  hybrid:
    decoding_size: 1
    enable_flowing: "" # store_true 类型的参数
    chunk_sizes: [2048, 2048, 256, 256, 128, 128, 128, 128]
    group_sizes: [2, 2]
    flowing_watermark: 0.10
    schedule_policy: "load_aware"
    # enable_zero_decoding: ""
    enable_zero_decoding: "--enable_zero_decoding"
    prefill_token_limit: [16384, 16384, 16384, 16384, 16384, 16384, 16384, 16384]
    decoding_policy: "simple_flowing"
    # decoding_policy: "prophet_flowing"
    disable_async_output: [True, True, False, False, False, False, False, False] # Pure P = True
  pd_disaggregation:
    decoding_size: 2
    chunk_sizes: [2048, 2048, 512, 512, 512, 512, 512, 512]
    group_sizes: []
    enable_flowing: "" # store_true 类型的参数
    flowing_watermark: 0.05
    schedule_policy: "load_aware"
    enable_zero_decoding: ""
    prefill_token_limit: [0]
    decoding_policy: "simple_flowing"
    disable_async_output: [True, True, False, False, False, False, False, False] # Pure P = True
  chunked_prefill:
    decoding_size: 0
    chunk_sizes: [512, 512, 512, 512, 512, 512, 512, 512]
    group_sizes: []
    enable_flowing: "" # store_true 类型的参数
    flowing_watermark: 0.05
    schedule_policy: "load_aware"
    enable_zero_decoding: ""
    prefill_token_limit: [0]
    decoding_policy: "simple_flowing"
    disable_async_output: [False, False, False, False, False, False, False, False] # Pure P = True

### 平台特殊配置 (高优先级) ###
platforms: 
  "cwang-X99": # X99 双卡 3060 平台
    qps_list: [10]
    num_prompts: 64
    selected_model: 0

  "992cdc39c3cc": # 5 卡 4090 平台
    qps_list: [10]
    num_prompts: 64
    selected_model: 0
