### 配置数据集负载 ###
# 14B, 4 卡 A800, ShareGPT
selected_dataset: 5

qps_list: [6] # [18, 20, 22, 24, 26, 28, 30, 32]
num_prompts: 256

### 配置指标 ###
steady_state_start: 0
steady_state_end: 1
ttft_slo: 2.5
tpot_slo: 0.060

slo_scale_qps: 2.0
slo_scale_list: [2.2, 2.0, 1.8, 1.6, 1.4, 1.2, 1.0, 0.8, 0.6]

### 配置模型 ###
selected_model: 1
world_size: 4
tp_size: 1

watermark: 0.05

### 配置测试对象 ###
schedulers:
  hybrid:
    decoding_size: 1
    enable_flowing: "--enable_flowing" # store_true 类型的参数
    chunk_sizes: [2048, 256, 256, 128, 128, 128, 128, 128]
    flowing_watermark: 0.10
    schedule_policy: "load_aware"
    enable_zero_decoding: ""
    # enable_zero_decoding: "--enable_zero_decoding"
    prefill_token_limit: [8192, 8192, 8192, 300, 8192, 8192, 8192, 8192]
    decoding_policy: "simple_flowing"
    # decoding_policy: "prophet_flowing"
  pd_disaggregation:
    decoding_size: 2
    chunk_sizes: [2048, 2048, 512, 512, 512, 512, 512, 512]
    enable_flowing: "" # store_true 类型的参数
    flowing_watermark: 0.05
    schedule_policy: "load_aware"
    enable_zero_decoding: ""
    prefill_token_limit: [0]
    decoding_policy: "simple_flowing"
  chunked_prefill:
    decoding_size: 0
    chunk_sizes: [256, 256, 256, 256, 512, 512, 512, 512]
    enable_flowing: "" # store_true 类型的参数
    flowing_watermark: 0.05
    schedule_policy: "load_aware"
    enable_zero_decoding: ""
    prefill_token_limit: [0]
    decoding_policy: "simple_flowing"

### 平台特殊配置 (高优先级) ###
platforms: 
  "cwang-X99": # X99 双卡 3060 平台
    qps_list: [10]
    num_prompts: 64
    selected_model: 0

  "992cdc39c3cc": # 5 卡 4090 平台
    qps_list: [10]
    num_prompts: 64
    selected_model: 0
