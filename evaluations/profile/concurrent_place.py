import time
import threading
from torch import distributed as dist
from vllm.distributed import parallel_state
from vllm.config import VllmConfig
from vllm.engine.arg_utils import EngineArgs
from vllm.sequence import SequenceData, SequenceGroupMetadata, SamplingParams
from vllm.model_executor.model_loader.utils import set_default_torch_dtype
from vllm.worker.worker import ModelRunner
from vllm.utils import get_kv_cache_torch_dtype, cdiv

from vllm import _custom_ops as ops

from tests.kernels.utils import make_kv_cache
import torch

import kv_placer

import nvtx

nvtx_profiler = nvtx.Profile()


class LayerInfo:
    """模型层信息, 用于保存 KV Cache"""

    def __init__(self, model_executable: torch.nn.Module):
        self.start_layer = model_executable.model.start_layer
        self.end_layer = model_executable.model.end_layer
        self.layer_attn = []
        for i in range(self.start_layer, self.end_layer):
            layer = model_executable.model.layers[i]
            attn_info = (
                layer.self_attn.attn.kv_cache_dtype,
                layer.self_attn.attn._k_scale,
                layer.self_attn.attn._v_scale,
            )
            self.layer_attn.append(attn_info)


def build_place_input(token_num: int, config: VllmConfig, num_base_blocks: int):
    # 返回 slot mapping 和 kv
    num_layers = config.model_config.get_num_layers(config.parallel_config)
    model_dtype = config.model_config.dtype
    kv_dtype = config.cache_config.cache_dtype
    dtype = get_kv_cache_torch_dtype(kv_dtype, model_dtype)
    device = config.device_config.device
    num_kv_heads = config.model_config.get_num_kv_heads(config.parallel_config)
    head_size = config.model_config.get_head_size()

    slot_mapping_list = list(range(num_base_blocks, num_base_blocks + token_num))
    slot_mapping = torch.tensor(slot_mapping_list, device=device)

    key = torch.ones(
        (num_layers, token_num, num_kv_heads, head_size), dtype=dtype, device=device
    )
    value = torch.ones(
        (num_layers, token_num, num_kv_heads, head_size), dtype=dtype, device=device
    )

    return slot_mapping, key, value


def place(
    layer_info: LayerInfo,
    key_cache_layer_ptrs: torch.Tensor,
    value_cache_layer_ptrs: torch.Tensor,
    block_size: int,
    block_stride: int,
    slot_mapping: torch.Tensor,
    key: torch.Tensor,
    value: torch.Tensor,
):
    """将读取的 kv cache 存入 block table"""
    kv_placer.fused_reshape_and_cache_flash_layers(
        key, value, key_cache_layer_ptrs, value_cache_layer_ptrs, block_size, block_stride, slot_mapping, "auto", 1.0, 1.0
    )

    # for i in range(layer_info.start_layer, layer_info.end_layer):
    #     # 写入位置
    #     kv_cache = kv_caches[i - layer_info.start_layer]
    #     key_cache, value_cache = kv_cache[0], kv_cache[1]
    #     layer_attn = layer_info.layer_attn[i - layer_info.start_layer]
    #     # 写入到 block table
    #     ops.reshape_and_cache_flash(
    #         key[i - layer_info.start_layer],
    #         value[i - layer_info.start_layer],
    #         key_cache,
    #         value_cache,
    #         slot_mapping,
    #         layer_attn[0],
    #         layer_attn[1],
    #         layer_attn[2],
    #     )


def build_decode_seq_group_metadatas(seq_lens: list[int], block_size: int = 16):
    batch = len(seq_lens)
    seq_datas = [SequenceData.from_seqs(range(seq_len)) for seq_len in seq_lens]
    for seq_data, seq_len in zip(seq_datas, seq_lens):
        seq_data.update_num_computed_tokens(seq_len)
        seq_data.append_token_id(1, 0)
    # we allocate one more block to avoid index out of range
    block_lens = [cdiv(seq_len, block_size) + 1 for seq_len in seq_lens]
    prefix_block_lens = [sum(block_lens[:i]) for i in range(len(block_lens))]
    block_tables = [
        list(range(prefix_len, prefix_len + block_len))
        for block_len, prefix_len in zip(block_lens, prefix_block_lens)
    ]  # 块号序列
    seq_group_metadata_list = [
        SequenceGroupMetadata(
            request_id=f"test_{i}",
            is_prompt=False,
            seq_data={0: seq_datas[i]},
            sampling_params=SamplingParams(temperature=0),
            block_tables={0: block_tables[i]},
        )
        for i in range(batch)
    ]
    return seq_group_metadata_list


def build_kv_caches(config: VllmConfig, num_blocks: int, backend: str):
    model_dtype = config.model_config.dtype
    kv_dtype = config.cache_config.cache_dtype
    device = config.device_config.device
    num_kv_heads = config.model_config.get_num_kv_heads(config.parallel_config)
    head_size = config.model_config.get_head_size()
    num_layers = config.model_config.get_num_layers(config.parallel_config)
    block_size = config.cache_config.block_size

    with set_default_torch_dtype(get_kv_cache_torch_dtype(kv_dtype, model_dtype)):
        kv_caches = [
            make_kv_cache(
                num_blocks, num_kv_heads, head_size, block_size, device, backend
            )
            for _ in range(num_layers)
        ]
    return kv_caches

def prepare_decode_model_runner_input(
    config: VllmConfig,
    seq_lens: list[int],
    model_runner: ModelRunner,
):
    block_size = config.cache_config.block_size
    seq_group_metadata_list = build_decode_seq_group_metadatas(
        seq_lens, block_size
    )
    num_blocks = sum([len(sg.block_tables[0]) for sg in seq_group_metadata_list])
    for seq_group_metadata in seq_group_metadata_list:
        assert seq_group_metadata.token_chunk_size == 1
    model_input = model_runner.prepare_model_input(seq_group_metadata_list)
    kv_caches = build_kv_caches(config, num_blocks * 2, "FLASH_ATTN")
    return model_input, kv_caches, num_blocks

def init_parallel_groups(rank: int, world_size: int, init_method: str):
    dist.init_process_group(
        backend=dist.Backend.GLOO,
        rank=rank,
        world_size=world_size,
        init_method=init_method,
    )
    parallel_state._PP = parallel_state.init_model_parallel_group(
        [[0]], 0, dist.Backend.NCCL, use_custom_allreduce=False, group_name="pp"
    )
    parallel_state._TP = parallel_state.init_model_parallel_group(
        [[0]], 0, dist.Backend.NCCL, use_message_queue_broadcaster=True, group_name="tp"
    )

def destroy_parallel_groups():
    parallel_state.destroy_model_parallel()
    dist.destroy_process_group()


class ConcurrentPlaceTest:
    def __init__(self):
        # 构建输入
        enforce_eager = False
        engine_args = EngineArgs(
            "Qwen/Qwen2.5-3B", enforce_eager=enforce_eager, load_format="dummy"
        )
        config = engine_args.create_engine_config()
        batch = 54
        seq_lens = [1280 for _ in range(batch)]
        init_parallel_groups(0, 1, "tcp://localhost:12346")
        model_runner = ModelRunner(config, is_driver_worker=True)
        model_runner.load_model()
        model_input, kv_caches, num_base_blocks = prepare_decode_model_runner_input(
            config, seq_lens, model_runner
        )

        if not enforce_eager:
            kv_caches_for_capture = [kv_caches]
            model_runner.capture_model(kv_caches_for_capture)

        output = model_runner.execute_model(model_input, kv_caches)  # 预热

        self.model_runner = model_runner
        self.model_input = model_input
        self.kv_caches = kv_caches

        self.layer_info = LayerInfo(model_runner.model)
        self.slot_mapping, self.key, self.value = build_place_input(
            1280, config, num_base_blocks
        )

        self.model_loop_count = 3
        self.place_loop_count = 20

    def run(self):
        main_thread = threading.Thread(target=self.main_thread_func)
        place_thread = threading.Thread(target=self.place_thread_func)

        main_thread.start()
        place_thread.start()

        main_thread.join()
        place_thread.join()

    def start_profile(self):
        nvtx_profiler.enable()  # 记录函数栈
        torch.cuda.profiler.start()  # profile 范围开始

    def stop_profile(self):
        torch.cuda.profiler.stop()  # profile 范围结束
        nvtx_profiler.disable()  # 停止记录函数栈

    def main_thread_func(self):
        # nvtx_profiler.enable()  # 记录函数栈

        for _ in range(self.model_loop_count):
            output = self.model_runner.execute_model(self.model_input, self.kv_caches)

        # nvtx_profiler.disable()  # 停止记录函数栈

    def place_thread_func(self):
        self.start_profile()
        
        # kv_caches is layers of [2, num_blocks, block_size, num_heads, head_size]
        
        key_cache_layer_ptrs = torch.tensor([kv_cache[0].data_ptr() for kv_cache in self.kv_caches], dtype=torch.int64, device="cuda:0")
        value_cache_layer_ptrs = torch.tensor([kv_cache[1].data_ptr() for kv_cache in self.kv_caches], dtype=torch.int64, device="cuda:0")

        block_size = self.kv_caches[0].size(2)
        block_stride = self.kv_caches[0].stride(1)

        for _ in range(self.place_loop_count):
            start_time = time.time()
            place(
                self.layer_info, key_cache_layer_ptrs, value_cache_layer_ptrs,
                block_size, block_stride,
                self.slot_mapping, self.key, self.value
            )
            print(f"place time: {time.time() - start_time}")

        self.stop_profile()


if __name__ == "__main__":
    ConcurrentPlaceTest().run()
