{"cells": [{"cell_type": "code", "execution_count": null, "id": "0ed8a08a", "metadata": {}, "outputs": [], "source": ["import re\n", "import numpy as np\n", "\n", "\n", "def extract_log_data(log_file_path):\n", "    with open(log_file_path, \"r\") as file:\n", "        log_content = file.read()\n", "\n", "    # Combine all lines into one\n", "    combined_log = log_content.replace(\"\\n\", \" \")\n", "\n", "    # Regex to extract the required pattern\n", "    pattern = r\"batch size (\\d+).*?Function execute_model took ([\\d.]+) seconds to complete Function execute_model took ([\\d.]+) seconds to complete\"\n", "    matches = re.findall(pattern, combined_log)\n", "\n", "    # Format the output\n", "    results = [\n", "        (batch_size, (float(time1) + float(time2)) / 2)\n", "        for batch_size, time1, time2 in matches\n", "    ]\n", "\n", "    return results\n", "\n", "\n", "def plot_batch_size_vs_time(output):\n", "    import matplotlib.pyplot as plt\n", "\n", "    # Extract batch sizes and average times\n", "    batch_sizes = [int(batch) for batch, avg_time in output]\n", "    avg_times = [avg_time for batch, avg_time in output]\n", "\n", "    # Line plot\n", "    plt.plot(batch_sizes, avg_times, color=\"blue\", marker=\"o\", label=\"Average Time\")\n", "\n", "    # Add labels and legend\n", "    plt.xlabel(\"Batch Size\")\n", "    plt.ylabel(\"Average Time (seconds)\")\n", "    plt.title(\"<PERSON>ch <PERSON> vs Average Time\")\n", "    plt.legend()\n", "\n", "    plt.xticks(batch_sizes)\n", "    plt.ylim(0, 0.060)\n", "\n", "    plt.grid(True)\n", "\n", "    # Show the plot\n", "    plt.show()\n", "\n", "\n", "# Example usage\n", "log_file_path = \"intensity.txt\"  # Replace with your log file path\n", "output = extract_log_data(log_file_path)\n", "\n", "plot_batch_size_vs_time(output)"]}, {"cell_type": "code", "execution_count": null, "id": "b3e38226", "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "PD-vllm", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}