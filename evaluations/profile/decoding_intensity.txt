INFO 04-22 08:51:20 config.py:352] This model supports multiple tasks: {'generate', 'embedding'}. Defaulting to 'generate'.
INFO 04-22 08:51:20 config.py:1144] Chunked prefill is enabled with max_num_batched_tokens=4096.
INFO 04-22 08:51:20 selector.py:135] Using Flash Attention backend.
INFO 04-22 08:51:20 model_runner.py:1144] Starting to load model Qwen/Qwen2.5-14B...
INFO 04-22 08:51:21 model_runner.py:1149] Loading model weights took 27.5914 GB
batch size 32
num_blocks 96
INFO 04-22 08:51:22 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:51:22 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:51:42 model_runner.py:1598] Graph capturing finished in 19 secs, took 1.24 GiB
预热
Function execute_model took 0.078 seconds to complete
执行
Function execute_model took 0.023 seconds to complete
Function execute_model took 0.023 seconds to complete
batch size 48
num_blocks 144
INFO 04-22 08:51:44 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:51:44 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:52:02 model_runner.py:1598] Graph capturing finished in 18 secs, took 0.12 GiB
预热
Function execute_model took 0.025 seconds to complete
执行
Function execute_model took 0.023 seconds to complete
Function execute_model took 0.023 seconds to complete
batch size 64
num_blocks 192
INFO 04-22 08:52:06 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:52:06 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:52:24 model_runner.py:1598] Graph capturing finished in 19 secs, took 0.08 GiB
预热
Function execute_model took 0.029 seconds to complete
执行
Function execute_model took 0.024 seconds to complete
Function execute_model took 0.024 seconds to complete
batch size 80
num_blocks 240
INFO 04-22 08:52:27 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:52:27 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:52:46 model_runner.py:1598] Graph capturing finished in 19 secs, took 0.08 GiB
预热
Function execute_model took 0.030 seconds to complete
执行
Function execute_model took 0.028 seconds to complete
Function execute_model took 0.028 seconds to complete
batch size 96
num_blocks 288
INFO 04-22 08:52:50 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:52:50 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:53:08 model_runner.py:1598] Graph capturing finished in 18 secs, took -0.24 GiB
预热
Function execute_model took 0.030 seconds to complete
执行
Function execute_model took 0.029 seconds to complete
Function execute_model took 0.029 seconds to complete
batch size 112
num_blocks 336
INFO 04-22 08:53:12 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:53:12 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:53:31 model_runner.py:1598] Graph capturing finished in 19 secs, took -0.64 GiB
预热
Function execute_model took 0.701 seconds to complete
执行
Function execute_model took 0.029 seconds to complete
Function execute_model took 0.031 seconds to complete
batch size 128
num_blocks 384
INFO 04-22 08:53:36 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:53:36 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:53:54 model_runner.py:1598] Graph capturing finished in 18 secs, took -0.58 GiB
预热
Function execute_model took 0.031 seconds to complete
执行
Function execute_model took 0.030 seconds to complete
Function execute_model took 0.030 seconds to complete
batch size 144
num_blocks 432
INFO 04-22 08:54:00 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:54:00 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:54:18 model_runner.py:1598] Graph capturing finished in 18 secs, took -0.70 GiB
预热
Function execute_model took 0.042 seconds to complete
执行
Function execute_model took 0.041 seconds to complete
Function execute_model took 0.039 seconds to complete
batch size 160
num_blocks 480
INFO 04-22 08:54:24 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:54:24 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:54:42 model_runner.py:1598] Graph capturing finished in 18 secs, took -0.63 GiB
预热
Function execute_model took 0.343 seconds to complete
执行
Function execute_model took 0.042 seconds to complete
Function execute_model took 0.042 seconds to complete
batch size 176
num_blocks 528
INFO 04-22 08:54:50 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:54:50 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:55:08 model_runner.py:1598] Graph capturing finished in 18 secs, took -0.69 GiB
预热
Function execute_model took 0.043 seconds to complete
执行
Function execute_model took 0.043 seconds to complete
Function execute_model took 0.040 seconds to complete
batch size 192
num_blocks 576
INFO 04-22 08:55:15 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:55:15 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:55:34 model_runner.py:1598] Graph capturing finished in 19 secs, took -0.75 GiB
预热
Function execute_model took 0.803 seconds to complete
执行
Function execute_model took 0.044 seconds to complete
Function execute_model took 0.043 seconds to complete
batch size 208
num_blocks 624
INFO 04-22 08:55:43 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:55:43 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:56:02 model_runner.py:1598] Graph capturing finished in 19 secs, took -0.87 GiB
预热
Function execute_model took 0.046 seconds to complete
执行
Function execute_model took 0.046 seconds to complete
Function execute_model took 0.043 seconds to complete
batch size 224
num_blocks 672
INFO 04-22 08:56:11 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:56:11 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:56:30 model_runner.py:1598] Graph capturing finished in 18 secs, took -0.77 GiB
预热
Function execute_model took 0.573 seconds to complete
执行
Function execute_model took 0.047 seconds to complete
Function execute_model took 0.047 seconds to complete
batch size 240
num_blocks 720
INFO 04-22 08:56:40 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:56:40 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:56:59 model_runner.py:1598] Graph capturing finished in 19 secs, took -0.86 GiB
预热
Function execute_model took 0.048 seconds to complete
执行
Function execute_model took 0.048 seconds to complete
Function execute_model took 0.048 seconds to complete
batch size 256
num_blocks 768
INFO 04-22 08:57:09 model_runner.py:1476] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI.
INFO 04-22 08:57:09 model_runner.py:1480] If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.
INFO 04-22 08:57:27 model_runner.py:1598] Graph capturing finished in 19 secs, took -0.58 GiB
预热
Function execute_model took 0.049 seconds to complete
执行
Function execute_model took 0.048 seconds to complete
Function execute_model took 0.043 seconds to complete
