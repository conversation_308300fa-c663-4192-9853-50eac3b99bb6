from torch import distributed as dist
from vllm.distributed import parallel_state
from vllm.config import VllmConfig
from vllm.engine.arg_utils import EngineArgs
from vllm.sequence import SequenceData, SequenceGroupMetadata, SamplingParams
from vllm.model_executor.model_loader.utils import set_default_torch_dtype
from vllm.worker.worker import ModelRunner
from vllm.utils import get_kv_cache_torch_dtype, cdiv

from tests.kernels.utils import make_kv_cache
import torch

import nvtx

nvtx_profiler = nvtx.Profile()


def build_prefill_seq_group_metadatas(seq_lens: list[int], block_size: int = 16):
    batch = len(seq_lens)
    seq_datas = [SequenceData.from_seqs(range(seq_len)) for seq_len in seq_lens]
    # for seq_data, seq_len in zip(seq_datas, seq_lens):
    # seq_data.update_num_computed_tokens(0)
    # seq_data.append_token_id(1, 0)
    block_lens = [cdiv(seq_len, block_size) for seq_len in seq_lens]
    prefix_block_lens = [sum(block_lens[:i]) for i in range(len(block_lens))]
    block_tables = [
        list(range(prefix_len, prefix_len + block_len))
        for block_len, prefix_len in zip(block_lens, prefix_block_lens)
    ]
    seq_group_metadata_list = [
        SequenceGroupMetadata(
            request_id=f"test_{i}",
            is_prompt=True,
            seq_data={0: seq_datas[i]},
            sampling_params=SamplingParams(temperature=0),
            block_tables={0: block_tables[i]},
        )
        for i in range(batch)
    ]
    return seq_group_metadata_list


def build_decode_seq_group_metadatas(seq_lens: list[int], block_size: int = 16):
    batch = len(seq_lens)
    seq_datas = [SequenceData.from_seqs(range(seq_len)) for seq_len in seq_lens]
    for seq_data, seq_len in zip(seq_datas, seq_lens):
        seq_data.update_num_computed_tokens(seq_len)
        seq_data.append_token_id(1, 0)
    # we allocate one more block to avoid index out of range
    block_lens = [cdiv(seq_len, block_size) + 1 for seq_len in seq_lens]
    prefix_block_lens = [sum(block_lens[:i]) for i in range(len(block_lens))]
    block_tables = [
        list(range(prefix_len, prefix_len + block_len))
        for block_len, prefix_len in zip(block_lens, prefix_block_lens)
    ]
    seq_group_metadata_list = [
        SequenceGroupMetadata(
            request_id=f"test_{i}",
            is_prompt=False,
            seq_data={0: seq_datas[i]},
            sampling_params=SamplingParams(temperature=0),
            block_tables={0: block_tables[i]},
        )
        for i in range(batch)
    ]
    return seq_group_metadata_list


def build_kv_caches(config: VllmConfig, num_blocks: int, backend: str):
    model_dtype = config.model_config.dtype
    kv_dtype = config.cache_config.cache_dtype
    device = config.device_config.device
    num_kv_heads = config.model_config.get_num_kv_heads(config.parallel_config)
    head_size = config.model_config.get_head_size()
    num_layers = config.model_config.get_num_layers(config.parallel_config)
    block_size = config.cache_config.block_size

    with set_default_torch_dtype(get_kv_cache_torch_dtype(kv_dtype, model_dtype)):
        kv_caches = [
            make_kv_cache(
                num_blocks, num_kv_heads, head_size, block_size, device, backend
            )
            for _ in range(num_layers)
        ]
    return kv_caches


def prepare_prefill_model_runner_input(
    config: VllmConfig,
    seq_lens: list[int],
    model_runner: ModelRunner,
):
    block_size = config.cache_config.block_size
    seq_group_metadata_list = build_prefill_seq_group_metadatas(seq_lens, block_size)
    num_blocks = sum([len(sg.block_tables[0]) for sg in seq_group_metadata_list])
    model_input = model_runner.prepare_model_input(seq_group_metadata_list)
    kv_caches = build_kv_caches(config, num_blocks, "FLASH_ATTN")
    return model_input, kv_caches


def prepare_decode_model_runner_input(
    config: VllmConfig,
    seq_lens: list[int],
    model_runner: ModelRunner,
):
    block_size = config.cache_config.block_size
    seq_group_metadata_list = build_decode_seq_group_metadatas(seq_lens, block_size)
    num_blocks = sum([len(sg.block_tables[0]) for sg in seq_group_metadata_list])
    for seq_group_metadata in seq_group_metadata_list:
        assert seq_group_metadata.token_chunk_size == 1
    model_input = model_runner.prepare_model_input(seq_group_metadata_list)
    kv_caches = build_kv_caches(config, num_blocks, "FLASH_ATTN")
    return model_input, kv_caches


def init_parallel_groups(rank: int, world_size: int, init_method: str):
    dist.init_process_group(
        backend=dist.Backend.GLOO,
        rank=rank,
        world_size=world_size,
        init_method=init_method,
    )
    parallel_state._PP = parallel_state.init_model_parallel_group(
        [[0]], 0, dist.Backend.NCCL, use_custom_allreduce=False, group_name="pp"
    )
    parallel_state._TP = parallel_state.init_model_parallel_group(
        [[0]], 0, dist.Backend.NCCL, use_message_queue_broadcaster=True, group_name="tp"
    )


def destroy_parallel_groups():
    parallel_state.destroy_model_parallel()
    dist.destroy_process_group()


if __name__ == "__main__":
    engine_args = EngineArgs(
        "Qwen/Qwen2.5-14B",
        enforce_eager=True,
        load_format="dummy",
        enable_chunked_prefill=True,
        max_num_batched_tokens=4096,
    )
    config = engine_args.create_engine_config()
    batch = 1
    init_parallel_groups(0, 1, "tcp://localhost:12346")
    model_runner = ModelRunner(config, is_driver_worker=True)
    model_runner.load_model()

    seq_lens = [256 for _ in range(batch)]
    model_input, kv_caches = prepare_prefill_model_runner_input(
        config, seq_lens, model_runner
    )

    chunk_sizes = list(range(128, 1024 + 1, 8))

    for chunk_size in chunk_sizes:
        model_input, kv_caches = prepare_prefill_model_runner_input(
            config, [chunk_size], model_runner
        )
        print(f"chunk size: {chunk_size}")
        model_runner.execute_model(model_input, kv_caches)
        model_runner.execute_model(model_input, kv_caches)
        model_runner.execute_model(model_input, kv_caches)

    destroy_parallel_groups()
