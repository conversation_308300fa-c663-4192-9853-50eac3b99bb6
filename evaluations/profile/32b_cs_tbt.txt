config.json: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████| 664/664 [00:00<00:00, 2.75MB/s]
INFO 04-23 08:03:05 config.py:352] This model supports multiple tasks: {'generate', 'embedding'}. Defaulting to 'generate'.
INFO 04-23 08:03:05 config.py:1144] Chunked prefill is enabled with max_num_batched_tokens=4096.
WARNING 04-23 08:03:05 config.py:505] To see benefits of async output processing, enable CUDA graph. Since, enforce-eager is enabled, async output processor cannot be used
INFO 04-23 08:03:06 selector.py:135] Using Flash Attention backend.
INFO 04-23 08:03:06 model_runner.py:1144] Starting to load model Qwen/Qwen2.5-32B...
INFO 04-23 08:03:07 model_runner.py:1149] Loading model weights took 61.0607 GB
chunk size: 128
Function execute_model took 0.527 seconds to complete
Function execute_model took 0.056 seconds to complete
Function execute_model took 0.055 seconds to complete
chunk size: 136
Function execute_model took 0.070 seconds to complete
Function execute_model took 0.070 seconds to complete
Function execute_model took 0.070 seconds to complete
chunk size: 144
Function execute_model took 0.069 seconds to complete
Function execute_model took 0.070 seconds to complete
Function execute_model took 0.070 seconds to complete
chunk size: 152
Function execute_model took 0.071 seconds to complete
Function execute_model took 0.071 seconds to complete
Function execute_model took 0.070 seconds to complete
chunk size: 160
Function execute_model took 0.070 seconds to complete
Function execute_model took 0.071 seconds to complete
Function execute_model took 0.071 seconds to complete
chunk size: 168
Function execute_model took 0.072 seconds to complete
Function execute_model took 0.071 seconds to complete
Function execute_model took 0.071 seconds to complete
chunk size: 176
Function execute_model took 0.072 seconds to complete
Function execute_model took 0.079 seconds to complete
Function execute_model took 0.073 seconds to complete
chunk size: 184
Function execute_model took 0.068 seconds to complete
Function execute_model took 0.081 seconds to complete
Function execute_model took 0.073 seconds to complete
chunk size: 192
Function execute_model took 0.069 seconds to complete
Function execute_model took 0.084 seconds to complete
Function execute_model took 0.072 seconds to complete
chunk size: 200
Function execute_model took 0.071 seconds to complete
Function execute_model took 0.096 seconds to complete
Function execute_model took 0.074 seconds to complete
chunk size: 208
Function execute_model took 0.077 seconds to complete
Function execute_model took 0.076 seconds to complete
Function execute_model took 0.074 seconds to complete
chunk size: 216
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.076 seconds to complete
Function execute_model took 0.076 seconds to complete
chunk size: 224
Function execute_model took 0.091 seconds to complete
Function execute_model took 0.078 seconds to complete
Function execute_model took 0.078 seconds to complete
chunk size: 232
Function execute_model took 0.085 seconds to complete
Function execute_model took 0.078 seconds to complete
Function execute_model took 0.079 seconds to complete
chunk size: 240
Function execute_model took 0.073 seconds to complete
Function execute_model took 0.096 seconds to complete
Function execute_model took 0.075 seconds to complete
chunk size: 248
Function execute_model took 0.078 seconds to complete
Function execute_model took 0.083 seconds to complete
Function execute_model took 0.082 seconds to complete
chunk size: 256
Function execute_model took 0.089 seconds to complete
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.080 seconds to complete
chunk size: 264
Function execute_model took 0.469 seconds to complete
Function execute_model took 0.108 seconds to complete
Function execute_model took 0.108 seconds to complete
chunk size: 272
Function execute_model took 0.106 seconds to complete
Function execute_model took 0.117 seconds to complete
Function execute_model took 0.107 seconds to complete
chunk size: 280
Function execute_model took 0.104 seconds to complete
Function execute_model took 0.111 seconds to complete
Function execute_model took 0.108 seconds to complete
chunk size: 288
Function execute_model took 0.108 seconds to complete
Function execute_model took 0.107 seconds to complete
Function execute_model took 0.116 seconds to complete
chunk size: 296
Function execute_model took 0.330 seconds to complete
Function execute_model took 0.108 seconds to complete
Function execute_model took 0.108 seconds to complete
chunk size: 304
Function execute_model took 0.103 seconds to complete
Function execute_model took 0.118 seconds to complete
Function execute_model took 0.109 seconds to complete
chunk size: 312
Function execute_model took 0.109 seconds to complete
Function execute_model took 0.110 seconds to complete
Function execute_model took 0.110 seconds to complete
chunk size: 320
Function execute_model took 0.115 seconds to complete
Function execute_model took 0.110 seconds to complete
Function execute_model took 0.110 seconds to complete
chunk size: 328
Function execute_model took 0.110 seconds to complete
Function execute_model took 0.109 seconds to complete
Function execute_model took 0.111 seconds to complete
chunk size: 336
Function execute_model took 0.119 seconds to complete
Function execute_model took 0.111 seconds to complete
Function execute_model took 0.112 seconds to complete
chunk size: 344
Function execute_model took 0.119 seconds to complete
Function execute_model took 0.110 seconds to complete
Function execute_model took 0.118 seconds to complete
chunk size: 352
Function execute_model took 0.112 seconds to complete
Function execute_model took 0.111 seconds to complete
Function execute_model took 0.111 seconds to complete
chunk size: 360
Function execute_model took 0.111 seconds to complete
Function execute_model took 0.112 seconds to complete
Function execute_model took 0.111 seconds to complete
chunk size: 368
Function execute_model took 0.144 seconds to complete
Function execute_model took 0.135 seconds to complete
Function execute_model took 0.113 seconds to complete
chunk size: 376
Function execute_model took 0.112 seconds to complete
Function execute_model took 0.112 seconds to complete
Function execute_model took 0.115 seconds to complete
chunk size: 384
Function execute_model took 0.114 seconds to complete
Function execute_model took 0.114 seconds to complete
Function execute_model took 0.114 seconds to complete
chunk size: 392
Function execute_model took 0.146 seconds to complete
Function execute_model took 0.137 seconds to complete
Function execute_model took 0.138 seconds to complete
chunk size: 400
Function execute_model took 0.318 seconds to complete
Function execute_model took 0.140 seconds to complete
Function execute_model took 0.138 seconds to complete
chunk size: 408
Function execute_model took 0.141 seconds to complete
Function execute_model took 0.142 seconds to complete
Function execute_model took 0.139 seconds to complete
chunk size: 416
Function execute_model took 0.146 seconds to complete
Function execute_model took 0.145 seconds to complete
Function execute_model took 0.141 seconds to complete
chunk size: 424
Function execute_model took 0.150 seconds to complete
Function execute_model took 0.142 seconds to complete
Function execute_model took 0.144 seconds to complete
chunk size: 432
Function execute_model took 0.151 seconds to complete
Function execute_model took 0.146 seconds to complete
Function execute_model took 0.144 seconds to complete
chunk size: 440
Function execute_model took 0.153 seconds to complete
Function execute_model took 0.144 seconds to complete
Function execute_model took 0.143 seconds to complete
chunk size: 448
Function execute_model took 0.149 seconds to complete
Function execute_model took 0.144 seconds to complete
Function execute_model took 0.144 seconds to complete
chunk size: 456
Function execute_model took 0.159 seconds to complete
Function execute_model took 0.143 seconds to complete
Function execute_model took 0.143 seconds to complete
chunk size: 464
Function execute_model took 0.143 seconds to complete
Function execute_model took 0.143 seconds to complete
Function execute_model took 0.143 seconds to complete
chunk size: 472
Function execute_model took 0.142 seconds to complete
Function execute_model took 0.143 seconds to complete
Function execute_model took 0.144 seconds to complete
chunk size: 480
Function execute_model took 0.143 seconds to complete
Function execute_model took 0.144 seconds to complete
Function execute_model took 0.144 seconds to complete
chunk size: 488
Function execute_model took 0.145 seconds to complete
Function execute_model took 0.143 seconds to complete
Function execute_model took 0.146 seconds to complete
chunk size: 496
Function execute_model took 0.144 seconds to complete
Function execute_model took 0.144 seconds to complete
Function execute_model took 0.144 seconds to complete
chunk size: 504
Function execute_model took 0.152 seconds to complete
Function execute_model took 0.144 seconds to complete
Function execute_model took 0.145 seconds to complete
chunk size: 512
Function execute_model took 0.153 seconds to complete
Function execute_model took 0.145 seconds to complete
Function execute_model took 0.145 seconds to complete
chunk size: 520
Function execute_model took 0.465 seconds to complete
Function execute_model took 0.176 seconds to complete
Function execute_model took 0.176 seconds to complete
chunk size: 528
Function execute_model took 0.187 seconds to complete
Function execute_model took 0.176 seconds to complete
Function execute_model took 0.177 seconds to complete
chunk size: 536
Function execute_model took 0.179 seconds to complete
Function execute_model took 0.180 seconds to complete
Function execute_model took 0.178 seconds to complete
chunk size: 544
Function execute_model took 0.178 seconds to complete
Function execute_model took 0.184 seconds to complete
Function execute_model took 0.180 seconds to complete
chunk size: 552
Function execute_model took 0.189 seconds to complete
Function execute_model took 0.180 seconds to complete
Function execute_model took 0.180 seconds to complete
chunk size: 560
Function execute_model took 0.197 seconds to complete
Function execute_model took 0.181 seconds to complete
Function execute_model took 0.180 seconds to complete
chunk size: 568
Function execute_model took 0.179 seconds to complete
Function execute_model took 0.181 seconds to complete
Function execute_model took 0.180 seconds to complete
chunk size: 576
Function execute_model took 0.182 seconds to complete
Function execute_model took 0.181 seconds to complete
Function execute_model took 0.182 seconds to complete
chunk size: 584
Function execute_model took 0.189 seconds to complete
Function execute_model took 0.182 seconds to complete
Function execute_model took 0.181 seconds to complete
chunk size: 592
Function execute_model took 0.194 seconds to complete
Function execute_model took 0.187 seconds to complete
Function execute_model took 0.181 seconds to complete
chunk size: 600
Function execute_model took 0.199 seconds to complete
Function execute_model took 0.183 seconds to complete
Function execute_model took 0.183 seconds to complete
chunk size: 608
Function execute_model took 0.629 seconds to complete
Function execute_model took 0.184 seconds to complete
Function execute_model took 0.183 seconds to complete
chunk size: 616
Function execute_model took 0.184 seconds to complete
Function execute_model took 0.189 seconds to complete
Function execute_model took 0.183 seconds to complete
chunk size: 624
Function execute_model took 0.183 seconds to complete
Function execute_model took 0.184 seconds to complete
Function execute_model took 0.183 seconds to complete
chunk size: 632
Function execute_model took 0.201 seconds to complete
Function execute_model took 0.183 seconds to complete
Function execute_model took 0.184 seconds to complete
chunk size: 640
Function execute_model took 0.186 seconds to complete
Function execute_model took 0.184 seconds to complete
Function execute_model took 0.185 seconds to complete
chunk size: 648
Function execute_model took 0.466 seconds to complete
Function execute_model took 0.211 seconds to complete
Function execute_model took 0.212 seconds to complete
chunk size: 656
Function execute_model took 0.225 seconds to complete
Function execute_model took 0.215 seconds to complete
Function execute_model took 0.215 seconds to complete
chunk size: 664
Function execute_model took 0.484 seconds to complete
Function execute_model took 0.217 seconds to complete
Function execute_model took 0.216 seconds to complete
chunk size: 672
Function execute_model took 0.217 seconds to complete
Function execute_model took 0.215 seconds to complete
Function execute_model took 0.216 seconds to complete
chunk size: 680
Function execute_model took 0.231 seconds to complete
Function execute_model took 0.215 seconds to complete
Function execute_model took 0.217 seconds to complete
chunk size: 688
Function execute_model took 0.229 seconds to complete
Function execute_model took 0.217 seconds to complete
Function execute_model took 0.222 seconds to complete
chunk size: 696
Function execute_model took 0.225 seconds to complete
Function execute_model took 0.216 seconds to complete
Function execute_model took 0.216 seconds to complete
chunk size: 704
Function execute_model took 0.379 seconds to complete
Function execute_model took 0.216 seconds to complete
Function execute_model took 0.222 seconds to complete
chunk size: 712
Function execute_model took 0.226 seconds to complete
Function execute_model took 0.220 seconds to complete
Function execute_model took 0.218 seconds to complete
chunk size: 720
Function execute_model took 0.217 seconds to complete
Function execute_model took 0.230 seconds to complete
Function execute_model took 0.222 seconds to complete
chunk size: 728
Function execute_model took 0.219 seconds to complete
Function execute_model took 0.221 seconds to complete
Function execute_model took 0.220 seconds to complete
chunk size: 736
Function execute_model took 0.236 seconds to complete
Function execute_model took 0.223 seconds to complete
Function execute_model took 0.219 seconds to complete
chunk size: 744
Function execute_model took 0.239 seconds to complete
Function execute_model took 0.219 seconds to complete
Function execute_model took 0.219 seconds to complete
chunk size: 752
Function execute_model took 0.232 seconds to complete
Function execute_model took 0.230 seconds to complete
Function execute_model took 0.221 seconds to complete
chunk size: 760
Function execute_model took 0.223 seconds to complete
Function execute_model took 0.220 seconds to complete
Function execute_model took 0.220 seconds to complete
chunk size: 768
Function execute_model took 0.241 seconds to complete
Function execute_model took 0.220 seconds to complete
Function execute_model took 0.223 seconds to complete
chunk size: 776
Function execute_model took 0.260 seconds to complete
Function execute_model took 0.242 seconds to complete
Function execute_model took 0.243 seconds to complete
chunk size: 784
Function execute_model took 0.252 seconds to complete
Function execute_model took 0.242 seconds to complete
Function execute_model took 0.244 seconds to complete
chunk size: 792
Function execute_model took 0.243 seconds to complete
Function execute_model took 0.243 seconds to complete
Function execute_model took 0.243 seconds to complete
chunk size: 800
Function execute_model took 0.253 seconds to complete
Function execute_model took 0.244 seconds to complete
Function execute_model took 0.244 seconds to complete
chunk size: 808
Function execute_model took 0.249 seconds to complete
Function execute_model took 0.244 seconds to complete
Function execute_model took 0.244 seconds to complete
chunk size: 816
Function execute_model took 0.243 seconds to complete
Function execute_model took 0.245 seconds to complete
Function execute_model took 0.245 seconds to complete
chunk size: 824
Function execute_model took 0.262 seconds to complete
Function execute_model took 0.244 seconds to complete
Function execute_model took 0.245 seconds to complete
chunk size: 832
Function execute_model took 0.244 seconds to complete
Function execute_model took 0.245 seconds to complete
Function execute_model took 0.246 seconds to complete
chunk size: 840
Function execute_model took 0.385 seconds to complete
Function execute_model took 0.246 seconds to complete
Function execute_model took 0.247 seconds to complete
chunk size: 848
Function execute_model took 0.245 seconds to complete
Function execute_model took 0.246 seconds to complete
Function execute_model took 0.247 seconds to complete
chunk size: 856
Function execute_model took 0.497 seconds to complete
Function execute_model took 0.247 seconds to complete
Function execute_model took 0.247 seconds to complete
chunk size: 864
Function execute_model took 0.264 seconds to complete
Function execute_model took 0.247 seconds to complete
Function execute_model took 0.248 seconds to complete
chunk size: 872
Function execute_model took 0.264 seconds to complete
Function execute_model took 0.249 seconds to complete
Function execute_model took 0.250 seconds to complete
chunk size: 880
Function execute_model took 0.249 seconds to complete
Function execute_model took 0.248 seconds to complete
Function execute_model took 0.249 seconds to complete
chunk size: 888
Function execute_model took 0.268 seconds to complete
Function execute_model took 0.249 seconds to complete
Function execute_model took 0.250 seconds to complete
chunk size: 896
Function execute_model took 0.248 seconds to complete
Function execute_model took 0.257 seconds to complete
Function execute_model took 0.254 seconds to complete
chunk size: 904
Function execute_model took 0.288 seconds to complete
Function execute_model took 0.275 seconds to complete
Function execute_model took 0.274 seconds to complete
chunk size: 912
Function execute_model took 0.526 seconds to complete
Function execute_model took 0.274 seconds to complete
Function execute_model took 0.275 seconds to complete
chunk size: 920
Function execute_model took 0.283 seconds to complete
Function execute_model took 0.277 seconds to complete
Function execute_model took 0.275 seconds to complete
chunk size: 928
Function execute_model took 0.295 seconds to complete
Function execute_model took 0.278 seconds to complete
Function execute_model took 0.275 seconds to complete
chunk size: 936
Function execute_model took 0.276 seconds to complete
Function execute_model took 0.284 seconds to complete
Function execute_model took 0.276 seconds to complete
chunk size: 944
Function execute_model took 0.291 seconds to complete
Function execute_model took 0.277 seconds to complete
Function execute_model took 0.277 seconds to complete
chunk size: 952
Function execute_model took 0.283 seconds to complete
Function execute_model took 0.281 seconds to complete
Function execute_model took 0.276 seconds to complete
chunk size: 960
Function execute_model took 0.287 seconds to complete
Function execute_model took 0.278 seconds to complete
Function execute_model took 0.276 seconds to complete
chunk size: 968
Function execute_model took 0.563 seconds to complete
Function execute_model took 0.278 seconds to complete
Function execute_model took 0.278 seconds to complete
chunk size: 976
Function execute_model took 0.290 seconds to complete
Function execute_model took 0.278 seconds to complete
Function execute_model took 0.279 seconds to complete
chunk size: 984
Function execute_model took 0.277 seconds to complete
Function execute_model took 0.280 seconds to complete
Function execute_model took 0.279 seconds to complete
chunk size: 992
Function execute_model took 0.298 seconds to complete
Function execute_model took 0.284 seconds to complete
Function execute_model took 0.282 seconds to complete
chunk size: 1000
Function execute_model took 0.295 seconds to complete
Function execute_model took 0.280 seconds to complete
Function execute_model took 0.280 seconds to complete
chunk size: 1008
Function execute_model took 0.438 seconds to complete
Function execute_model took 0.290 seconds to complete
Function execute_model took 0.281 seconds to complete
chunk size: 1016
Function execute_model took 0.280 seconds to complete
Function execute_model took 0.280 seconds to complete
Function execute_model took 0.281 seconds to complete
chunk size: 1024
Function execute_model took 0.287 seconds to complete
Function execute_model took 0.280 seconds to complete
Function execute_model took 0.281 seconds to complete