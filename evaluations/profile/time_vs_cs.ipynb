{"cells": [{"cell_type": "code", "execution_count": null, "id": "59308b8d", "metadata": {}, "outputs": [], "source": ["import re\n", "import os\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 从文件中读取日志内容\n", "with open(\"./14b_cs_tbt.txt\", \"r\") as file:\n", "    log_data = file.read()\n", "\n", "log_data = log_data.replace(\"\\n\", \" \")\n", "\n", "print(log_data[:1000])\n", "\n", "# 使用正则表达式提取 chunk size 和时间\n", "pattern = r\"chunk size: (\\d+) Function execute_model took ([\\d.]+) seconds to complete Function execute_model took ([\\d.]+) seconds to complete Function execute_model took ([\\d.]+) seconds to complete\"\n", "matches = re.findall(pattern, log_data)\n", "\n", "# 将数据存储到字典中，按 chunk size 分组\n", "data = {}\n", "for chunk_size, _, _, time in matches:\n", "    chunk_size = int(chunk_size)\n", "    time = float(time)\n", "    if chunk_size not in data:\n", "        data[chunk_size] = []\n", "    data[chunk_size].append(time)\n", "\n", "# 计算每个 chunk size 的平均时间\n", "averages = {chunk_size: sum(times) / len(times) for chunk_size, times in data.items()}\n", "averages_ms = {chunk_size: time * 1000 for chunk_size, time in averages.items()}\n", "\n", "# 绘制折线图\n", "plt.figure(figsize=(4, 3))\n", "plt.plot(list(averages_ms.keys()), list(averages_ms.values()), marker=\"o\")\n", "plt.xlabel(\"Chunk Size\")\n", "plt.ylabel(\"Iteration Time (ms)\")\n", "plt.grid()\n", "\n", "plt.xticks(ticks=range(128, max(averages_ms.keys()) + 1, 128))\n", "\n", "# Create the .cache directory if it does not exist\n", "if not os.path.exists(\".cache\"):\n", "    os.makedirs(\".cache\")\n", "plt.savefig(\".cache/average_time_vs_chunk_size_ms.pdf\", format=\"pdf\")\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "6ece3dd2", "metadata": {}, "outputs": [], "source": ["chunk_sizes_interval = np.arange(128, 1024 + 1, 128)\n", "times_interval = [averages_ms.get(chunk_size, 0) for chunk_size in chunk_sizes_interval]\n", "\n", "\n", "# 线性拟合\n", "def linear_fit(x):\n", "    return coefficients[0] * x + coefficients[1]\n", "\n", "\n", "coefficients = np.polyfit(chunk_sizes_interval, times_interval, 1)\n", "# 计算拟合值\n", "fit_values = linear_fit(np.array(chunk_sizes_interval))\n", "# 计算拟合的斜率和截距\n", "slope = coefficients[0]\n", "intercept = coefficients[1]\n", "\n", "\n", "# 计算 R 方值\n", "r_squared = np.corrcoef(chunk_sizes_interval, times_interval)[0, 1] ** 2\n", "\n", "# 绘制散点图和拟合直线\n", "plt.figure(figsize=(4, 3))\n", "plt.scatter(chunk_sizes_interval, times_interval, color=\"blue\", label=\"Data Points\")\n", "plt.plot(\n", "    chunk_sizes_interval,\n", "    linear_fit(chunk_sizes_interval),\n", "    color=\"red\",\n", "    label=f\"Fit: y = {coefficients[0]:.2f}x + {coefficients[1]:.2f}\",\n", ")\n", "plt.xlabel(\"Chunk Size\")\n", "plt.ylabel(\"Iteration Time (ms)\")\n", "plt.legend()\n", "\n", "# # 在图中标注 R 方值\n", "# plt.text(0.05, 0.95, f\"$R^2 = {r_squared:.2f}$\", transform=plt.gca().transAxes, fontsize=10, verticalalignment='top')\n", "\n", "# 设置 x 轴刻度和网格间隔为 128\n", "plt.xticks(\n", "    ticks=np.arange(min(chunk_sizes_interval), max(chunk_sizes_interval) + 1, 128)\n", ")\n", "plt.grid(axis=\"both\", which=\"both\")\n", "plt.ylim(bottom=0)\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "287a6d6e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "PD-vllm", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}