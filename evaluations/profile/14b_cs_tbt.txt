INFO 04-23 09:48:55 config.py:352] This model supports multiple tasks: {'generate', 'embedding'}. Defaulting to 'generate'.
INFO 04-23 09:48:55 config.py:1144] Chunked prefill is enabled with max_num_batched_tokens=4096.
WARNING 04-23 09:48:55 config.py:505] To see benefits of async output processing, enable CUDA graph. Since, enforce-eager is enabled, async output processor cannot be used
INFO 04-23 09:48:56 selector.py:135] Using Flash Attention backend.
INFO 04-23 09:48:56 model_runner.py:1144] Starting to load model Qwen/Qwen2.5-14B...
INFO 04-23 09:48:56 model_runner.py:1149] Loading model weights took 27.5914 GB
chunk size: 128
Function execute_model took 0.498 seconds to complete
Function execute_model took 0.028 seconds to complete
Function execute_model took 0.028 seconds to complete
chunk size: 136
Function execute_model took 0.038 seconds to complete
Function execute_model took 0.038 seconds to complete
Function execute_model took 0.036 seconds to complete
chunk size: 144
Function execute_model took 0.033 seconds to complete
Function execute_model took 0.032 seconds to complete
Function execute_model took 0.033 seconds to complete
chunk size: 152
Function execute_model took 0.033 seconds to complete
Function execute_model took 0.032 seconds to complete
Function execute_model took 0.033 seconds to complete
chunk size: 160
Function execute_model took 0.033 seconds to complete
Function execute_model took 0.032 seconds to complete
Function execute_model took 0.034 seconds to complete
chunk size: 168
Function execute_model took 0.033 seconds to complete
Function execute_model took 0.033 seconds to complete
Function execute_model took 0.033 seconds to complete
chunk size: 176
Function execute_model took 0.033 seconds to complete
Function execute_model took 0.032 seconds to complete
Function execute_model took 0.035 seconds to complete
chunk size: 184
Function execute_model took 0.033 seconds to complete
Function execute_model took 0.033 seconds to complete
Function execute_model took 0.033 seconds to complete
chunk size: 192
Function execute_model took 0.033 seconds to complete
Function execute_model took 0.033 seconds to complete
Function execute_model took 0.036 seconds to complete
chunk size: 200
Function execute_model took 0.035 seconds to complete
Function execute_model took 0.035 seconds to complete
Function execute_model took 0.035 seconds to complete
chunk size: 208
Function execute_model took 0.035 seconds to complete
Function execute_model took 0.036 seconds to complete
Function execute_model took 0.036 seconds to complete
chunk size: 216
Function execute_model took 0.035 seconds to complete
Function execute_model took 0.035 seconds to complete
Function execute_model took 0.038 seconds to complete
chunk size: 224
Function execute_model took 0.036 seconds to complete
Function execute_model took 0.036 seconds to complete
Function execute_model took 0.038 seconds to complete
chunk size: 232
Function execute_model took 0.036 seconds to complete
Function execute_model took 0.036 seconds to complete
Function execute_model took 0.037 seconds to complete
chunk size: 240
Function execute_model took 0.036 seconds to complete
Function execute_model took 0.036 seconds to complete
Function execute_model took 0.038 seconds to complete
chunk size: 248
Function execute_model took 0.036 seconds to complete
Function execute_model took 0.038 seconds to complete
Function execute_model took 0.037 seconds to complete
chunk size: 256
Function execute_model took 0.036 seconds to complete
Function execute_model took 0.036 seconds to complete
Function execute_model took 0.037 seconds to complete
chunk size: 264
Function execute_model took 0.048 seconds to complete
Function execute_model took 0.049 seconds to complete
Function execute_model took 0.048 seconds to complete
chunk size: 272
Function execute_model took 0.048 seconds to complete
Function execute_model took 0.049 seconds to complete
Function execute_model took 0.048 seconds to complete
chunk size: 280
Function execute_model took 0.048 seconds to complete
Function execute_model took 0.049 seconds to complete
Function execute_model took 0.049 seconds to complete
chunk size: 288
Function execute_model took 0.048 seconds to complete
Function execute_model took 0.049 seconds to complete
Function execute_model took 0.049 seconds to complete
chunk size: 296
Function execute_model took 0.048 seconds to complete
Function execute_model took 0.049 seconds to complete
Function execute_model took 0.049 seconds to complete
chunk size: 304
Function execute_model took 0.048 seconds to complete
Function execute_model took 0.049 seconds to complete
Function execute_model took 0.052 seconds to complete
chunk size: 312
Function execute_model took 0.049 seconds to complete
Function execute_model took 0.049 seconds to complete
Function execute_model took 0.050 seconds to complete
chunk size: 320
Function execute_model took 0.048 seconds to complete
Function execute_model took 0.054 seconds to complete
Function execute_model took 0.053 seconds to complete
chunk size: 328
Function execute_model took 0.051 seconds to complete
Function execute_model took 0.051 seconds to complete
Function execute_model took 0.050 seconds to complete
chunk size: 336
Function execute_model took 0.049 seconds to complete
Function execute_model took 0.053 seconds to complete
Function execute_model took 0.051 seconds to complete
chunk size: 344
Function execute_model took 0.049 seconds to complete
Function execute_model took 0.048 seconds to complete
Function execute_model took 0.055 seconds to complete
chunk size: 352
Function execute_model took 0.050 seconds to complete
Function execute_model took 0.052 seconds to complete
Function execute_model took 0.050 seconds to complete
chunk size: 360
Function execute_model took 0.051 seconds to complete
Function execute_model took 0.052 seconds to complete
Function execute_model took 0.051 seconds to complete
chunk size: 368
Function execute_model took 0.050 seconds to complete
Function execute_model took 0.054 seconds to complete
Function execute_model took 0.050 seconds to complete
chunk size: 376
Function execute_model took 0.050 seconds to complete
Function execute_model took 0.051 seconds to complete
Function execute_model took 0.051 seconds to complete
chunk size: 384
Function execute_model took 0.050 seconds to complete
Function execute_model took 0.051 seconds to complete
Function execute_model took 0.051 seconds to complete
chunk size: 392
Function execute_model took 0.062 seconds to complete
Function execute_model took 0.063 seconds to complete
Function execute_model took 0.063 seconds to complete
chunk size: 400
Function execute_model took 0.063 seconds to complete
Function execute_model took 0.063 seconds to complete
Function execute_model took 0.063 seconds to complete
chunk size: 408
Function execute_model took 0.063 seconds to complete
Function execute_model took 0.063 seconds to complete
Function execute_model took 0.063 seconds to complete
chunk size: 416
Function execute_model took 0.065 seconds to complete
Function execute_model took 0.065 seconds to complete
Function execute_model took 0.065 seconds to complete
chunk size: 424
Function execute_model took 0.065 seconds to complete
Function execute_model took 0.065 seconds to complete
Function execute_model took 0.065 seconds to complete
chunk size: 432
Function execute_model took 0.064 seconds to complete
Function execute_model took 0.066 seconds to complete
Function execute_model took 0.066 seconds to complete
chunk size: 440
Function execute_model took 0.065 seconds to complete
Function execute_model took 0.067 seconds to complete
Function execute_model took 0.066 seconds to complete
chunk size: 448
Function execute_model took 0.064 seconds to complete
Function execute_model took 0.068 seconds to complete
Function execute_model took 0.066 seconds to complete
chunk size: 456
Function execute_model took 0.066 seconds to complete
Function execute_model took 0.065 seconds to complete
Function execute_model took 0.065 seconds to complete
chunk size: 464
Function execute_model took 0.064 seconds to complete
Function execute_model took 0.066 seconds to complete
Function execute_model took 0.065 seconds to complete
chunk size: 472
Function execute_model took 0.065 seconds to complete
Function execute_model took 0.066 seconds to complete
Function execute_model took 0.065 seconds to complete
chunk size: 480
Function execute_model took 0.065 seconds to complete
Function execute_model took 0.066 seconds to complete
Function execute_model took 0.067 seconds to complete
chunk size: 488
Function execute_model took 0.064 seconds to complete
Function execute_model took 0.066 seconds to complete
Function execute_model took 0.066 seconds to complete
chunk size: 496
Function execute_model took 0.067 seconds to complete
Function execute_model took 0.066 seconds to complete
Function execute_model took 0.066 seconds to complete
chunk size: 504
Function execute_model took 0.065 seconds to complete
Function execute_model took 0.066 seconds to complete
Function execute_model took 0.069 seconds to complete
chunk size: 512
Function execute_model took 0.068 seconds to complete
Function execute_model took 0.067 seconds to complete
Function execute_model took 0.066 seconds to complete
chunk size: 520
Function execute_model took 0.078 seconds to complete
Function execute_model took 0.079 seconds to complete
Function execute_model took 0.077 seconds to complete
chunk size: 528
Function execute_model took 0.077 seconds to complete
Function execute_model took 0.078 seconds to complete
Function execute_model took 0.079 seconds to complete
chunk size: 536
Function execute_model took 0.081 seconds to complete
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.081 seconds to complete
chunk size: 544
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.082 seconds to complete
chunk size: 552
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.079 seconds to complete
chunk size: 560
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.082 seconds to complete
Function execute_model took 0.081 seconds to complete
chunk size: 568
Function execute_model took 0.079 seconds to complete
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.080 seconds to complete
chunk size: 576
Function execute_model took 0.085 seconds to complete
Function execute_model took 0.082 seconds to complete
Function execute_model took 0.082 seconds to complete
chunk size: 584
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.083 seconds to complete
Function execute_model took 0.081 seconds to complete
chunk size: 592
Function execute_model took 0.086 seconds to complete
Function execute_model took 0.084 seconds to complete
Function execute_model took 0.080 seconds to complete
chunk size: 600
Function execute_model took 0.086 seconds to complete
Function execute_model took 0.082 seconds to complete
Function execute_model took 0.084 seconds to complete
chunk size: 608
Function execute_model took 0.081 seconds to complete
Function execute_model took 0.083 seconds to complete
Function execute_model took 0.082 seconds to complete
chunk size: 616
Function execute_model took 0.082 seconds to complete
Function execute_model took 0.084 seconds to complete
Function execute_model took 0.080 seconds to complete
chunk size: 624
Function execute_model took 0.084 seconds to complete
Function execute_model took 0.080 seconds to complete
Function execute_model took 0.082 seconds to complete
chunk size: 632
Function execute_model took 0.084 seconds to complete
Function execute_model took 0.081 seconds to complete
Function execute_model took 0.081 seconds to complete
chunk size: 640
Function execute_model took 0.091 seconds to complete
Function execute_model took 0.082 seconds to complete
Function execute_model took 0.083 seconds to complete
chunk size: 648
Function execute_model took 0.095 seconds to complete
Function execute_model took 0.095 seconds to complete
Function execute_model took 0.095 seconds to complete
chunk size: 656
Function execute_model took 0.097 seconds to complete
Function execute_model took 0.097 seconds to complete
Function execute_model took 0.097 seconds to complete
chunk size: 664
Function execute_model took 0.099 seconds to complete
Function execute_model took 0.098 seconds to complete
Function execute_model took 0.098 seconds to complete
chunk size: 672
Function execute_model took 0.096 seconds to complete
Function execute_model took 0.098 seconds to complete
Function execute_model took 0.098 seconds to complete
chunk size: 680
Function execute_model took 0.100 seconds to complete
Function execute_model took 0.097 seconds to complete
Function execute_model took 0.101 seconds to complete
chunk size: 688
Function execute_model took 0.098 seconds to complete
Function execute_model took 0.099 seconds to complete
Function execute_model took 0.099 seconds to complete
chunk size: 696
Function execute_model took 0.100 seconds to complete
Function execute_model took 0.099 seconds to complete
Function execute_model took 0.098 seconds to complete
chunk size: 704
Function execute_model took 0.100 seconds to complete
Function execute_model took 0.100 seconds to complete
Function execute_model took 0.100 seconds to complete
chunk size: 712
Function execute_model took 0.098 seconds to complete
Function execute_model took 0.099 seconds to complete
Function execute_model took 0.099 seconds to complete
chunk size: 720
Function execute_model took 0.101 seconds to complete
Function execute_model took 0.099 seconds to complete
Function execute_model took 0.099 seconds to complete
chunk size: 728
Function execute_model took 0.101 seconds to complete
Function execute_model took 0.098 seconds to complete
Function execute_model took 0.099 seconds to complete
chunk size: 736
Function execute_model took 0.099 seconds to complete
Function execute_model took 0.098 seconds to complete
Function execute_model took 0.100 seconds to complete
chunk size: 744
Function execute_model took 0.102 seconds to complete
Function execute_model took 0.099 seconds to complete
Function execute_model took 0.099 seconds to complete
chunk size: 752
Function execute_model took 0.103 seconds to complete
Function execute_model took 0.099 seconds to complete
Function execute_model took 0.099 seconds to complete
chunk size: 760
Function execute_model took 0.100 seconds to complete
Function execute_model took 0.100 seconds to complete
Function execute_model took 0.101 seconds to complete
chunk size: 768
Function execute_model took 0.111 seconds to complete
Function execute_model took 0.100 seconds to complete
Function execute_model took 0.100 seconds to complete
chunk size: 776
Function execute_model took 0.107 seconds to complete
Function execute_model took 0.108 seconds to complete
Function execute_model took 0.108 seconds to complete
chunk size: 784
Function execute_model took 0.107 seconds to complete
Function execute_model took 0.108 seconds to complete
Function execute_model took 0.108 seconds to complete
chunk size: 792
Function execute_model took 0.117 seconds to complete
Function execute_model took 0.109 seconds to complete
Function execute_model took 0.108 seconds to complete
chunk size: 800
Function execute_model took 0.109 seconds to complete
Function execute_model took 0.116 seconds to complete
Function execute_model took 0.113 seconds to complete
chunk size: 808
Function execute_model took 0.109 seconds to complete
Function execute_model took 0.109 seconds to complete
Function execute_model took 0.108 seconds to complete
chunk size: 816
Function execute_model took 0.107 seconds to complete
Function execute_model took 0.111 seconds to complete
Function execute_model took 0.115 seconds to complete
chunk size: 824
Function execute_model took 0.117 seconds to complete
Function execute_model took 0.109 seconds to complete
Function execute_model took 0.108 seconds to complete
chunk size: 832
Function execute_model took 0.114 seconds to complete
Function execute_model took 0.114 seconds to complete
Function execute_model took 0.111 seconds to complete
chunk size: 840
Function execute_model took 0.118 seconds to complete
Function execute_model took 0.109 seconds to complete
Function execute_model took 0.111 seconds to complete
chunk size: 848
Function execute_model took 0.117 seconds to complete
Function execute_model took 0.110 seconds to complete
Function execute_model took 0.110 seconds to complete
chunk size: 856
Function execute_model took 0.109 seconds to complete
Function execute_model took 0.111 seconds to complete
Function execute_model took 0.110 seconds to complete
chunk size: 864
Function execute_model took 0.116 seconds to complete
Function execute_model took 0.111 seconds to complete
Function execute_model took 0.109 seconds to complete
chunk size: 872
Function execute_model took 0.120 seconds to complete
Function execute_model took 0.110 seconds to complete
Function execute_model took 0.111 seconds to complete
chunk size: 880
Function execute_model took 0.112 seconds to complete
Function execute_model took 0.112 seconds to complete
Function execute_model took 0.112 seconds to complete
chunk size: 888
Function execute_model took 0.124 seconds to complete
Function execute_model took 0.111 seconds to complete
Function execute_model took 0.112 seconds to complete
chunk size: 896
Function execute_model took 0.113 seconds to complete
Function execute_model took 0.112 seconds to complete
Function execute_model took 0.107 seconds to complete
chunk size: 904
Function execute_model took 0.126 seconds to complete
Function execute_model took 0.123 seconds to complete
Function execute_model took 0.122 seconds to complete
chunk size: 912
Function execute_model took 0.123 seconds to complete
Function execute_model took 0.123 seconds to complete
Function execute_model took 0.122 seconds to complete
chunk size: 920
Function execute_model took 0.124 seconds to complete
Function execute_model took 0.123 seconds to complete
Function execute_model took 0.124 seconds to complete
chunk size: 928
Function execute_model took 0.122 seconds to complete
Function execute_model took 0.124 seconds to complete
Function execute_model took 0.123 seconds to complete
chunk size: 936
Function execute_model took 0.122 seconds to complete
Function execute_model took 0.124 seconds to complete
Function execute_model took 0.124 seconds to complete
chunk size: 944
Function execute_model took 0.123 seconds to complete
Function execute_model took 0.123 seconds to complete
Function execute_model took 0.124 seconds to complete
chunk size: 952
Function execute_model took 0.124 seconds to complete
Function execute_model took 0.127 seconds to complete
Function execute_model took 0.125 seconds to complete
chunk size: 960
Function execute_model took 0.124 seconds to complete
Function execute_model took 0.125 seconds to complete
Function execute_model took 0.125 seconds to complete
chunk size: 968
Function execute_model took 0.124 seconds to complete
Function execute_model took 0.124 seconds to complete
Function execute_model took 0.125 seconds to complete
chunk size: 976
Function execute_model took 0.128 seconds to complete
Function execute_model took 0.124 seconds to complete
Function execute_model took 0.125 seconds to complete
chunk size: 984
Function execute_model took 0.127 seconds to complete
Function execute_model took 0.125 seconds to complete
Function execute_model took 0.125 seconds to complete
chunk size: 992
Function execute_model took 0.128 seconds to complete
Function execute_model took 0.126 seconds to complete
Function execute_model took 0.125 seconds to complete
chunk size: 1000
Function execute_model took 0.129 seconds to complete
Function execute_model took 0.125 seconds to complete
Function execute_model took 0.125 seconds to complete
chunk size: 1008
Function execute_model took 0.125 seconds to complete
Function execute_model took 0.126 seconds to complete
Function execute_model took 0.125 seconds to complete
chunk size: 1016
Function execute_model took 0.125 seconds to complete
Function execute_model took 0.126 seconds to complete
Function execute_model took 0.126 seconds to complete
chunk size: 1024
Function execute_model took 0.133 seconds to complete
Function execute_model took 0.127 seconds to complete
Function execute_model took 0.126 seconds to complete
