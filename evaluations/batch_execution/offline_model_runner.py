from torch import distributed as dist
from vllm.distributed import parallel_state
from vllm.config import VllmConfig
from vllm.engine.arg_utils import EngineArgs
from vllm.sequence import (
    SequenceData,
    SequenceGroupMetadata,
    SamplingParams,
    ExecuteModelRequest,
)
from vllm.model_executor.model_loader.utils import set_default_torch_dtype
from vllm.worker.worker import ModelRunner
from vllm.utils import (
    get_kv_cache_torch_dtype,
    cdiv,
    get_distributed_init_method,
    get_open_port,
)
from vllm.executor.multiproc_gpu_executor import MultiprocessingGPUExecutor
from vllm.executor.gpu_executor import create_worker

# 将当前目录的 ../.. 置于 import 路径
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)) + "/../..")
from tests.kernels.utils import make_kv_cache
import torch

import nvtx

nvtx_profiler = nvtx.Profile()


def build_prefill_seq_group_metadatas(seq_lens: list[int], block_size: int = 16):
    batch = len(seq_lens)
    seq_datas = [SequenceData.from_seqs(range(seq_len)) for seq_len in seq_lens]
    # for seq_data, seq_len in zip(seq_datas, seq_lens):
    # seq_data.update_num_computed_tokens(0)
    # seq_data.append_token_id(1, 0)
    block_lens = [cdiv(seq_len, block_size) for seq_len in seq_lens]
    prefix_block_lens = [sum(block_lens[:i]) for i in range(len(block_lens))]
    block_tables = [
        list(range(prefix_len, prefix_len + block_len))
        for block_len, prefix_len in zip(block_lens, prefix_block_lens)
    ]
    seq_group_metadata_list = [
        SequenceGroupMetadata(
            request_id=f"test_{i}",
            is_prompt=True,
            seq_data={0: seq_datas[i]},
            sampling_params=SamplingParams(temperature=0),
            block_tables={0: block_tables[i]},
        )
        for i in range(batch)
    ]
    return seq_group_metadata_list


def build_decode_seq_group_metadatas(seq_lens: list[int], block_size: int = 16) -> list[SequenceGroupMetadata]:
    batch = len(seq_lens)
    seq_datas = [SequenceData.from_seqs(range(seq_len)) for seq_len in seq_lens]
    for seq_data, seq_len in zip(seq_datas, seq_lens):
        seq_data.update_num_computed_tokens(seq_len)
        seq_data.append_token_id(1, 0)
    # we allocate one more block to avoid index out of range
    block_lens = [cdiv(seq_len, block_size) + 1 for seq_len in seq_lens]
    prefix_block_lens = [sum(block_lens[:i]) for i in range(len(block_lens))]
    block_tables = [
        list(range(prefix_len, prefix_len + block_len))
        for block_len, prefix_len in zip(block_lens, prefix_block_lens)
    ]
    seq_group_metadata_list = [
        SequenceGroupMetadata(
            request_id=f"test_{i}",
            is_prompt=False,
            seq_data={0: seq_datas[i]},
            sampling_params=SamplingParams(temperature=0),
            block_tables={0: block_tables[i]},
        )
        for i in range(batch)
    ]
    return seq_group_metadata_list

def build_hybrid_seq_group_metadatas(seq_lens: list[int], block_size: int = 16) -> list[SequenceGroupMetadata]:
    batch = len(seq_lens)
    seq_datas = [SequenceData.from_seqs(range(seq_len)) for seq_len in seq_lens]
    for i, (seq_data, seq_len) in enumerate(zip(seq_datas, seq_lens)):
        if i < batch - 1:
            # decoding request
            seq_data.update_num_computed_tokens(seq_len)
            seq_data.append_token_id(1, 0)
        else:
            # prompt request
            # seq_data.update_num_computed_tokens(0)
            # seq_data.append_token_id(1, 0)
            pass

    # we allocate one more block to avoid index out of range
    block_lens = [cdiv(seq_len, block_size) + 1 for seq_len in seq_lens]
    prefix_block_lens = [sum(block_lens[:i]) for i in range(len(block_lens))]
    block_tables = [
        list(range(prefix_len, prefix_len + block_len))
        for block_len, prefix_len in zip(block_lens, prefix_block_lens)
    ]

    seq_group_metadata_list = []
    # add the last seq_data as a prompt
    seq_group_metadata_list.append(
        SequenceGroupMetadata(
            request_id=f"test_{batch-1}",
            is_prompt=True,
            seq_data={0: seq_datas[batch - 1]},
            sampling_params=SamplingParams(temperature=0),
            block_tables={0: block_tables[batch - 1]},
        )
    )
    seq_group_metadata_list += [
        SequenceGroupMetadata(
            request_id=f"test_{i}",
            is_prompt=False,
            seq_data={0: seq_datas[i]},
            sampling_params=SamplingParams(temperature=0),
            block_tables={0: block_tables[i]},
        )
        for i in range(batch - 1)
    ]

    print("token_chunk_size", seq_group_metadata_list[-1].token_chunk_size)
    return seq_group_metadata_list


def build_kv_caches(config: VllmConfig, num_blocks: int, backend: str):
    model_dtype = config.model_config.dtype
    kv_dtype = config.cache_config.cache_dtype
    device = config.device_config.device
    num_kv_heads = config.model_config.get_num_kv_heads(config.parallel_config)
    head_size = config.model_config.get_head_size()
    num_layers = config.model_config.get_num_layers(config.parallel_config)
    block_size = config.cache_config.block_size

    with set_default_torch_dtype(get_kv_cache_torch_dtype(kv_dtype, model_dtype)):
        kv_caches = [
            make_kv_cache(
                num_blocks, num_kv_heads, head_size, block_size, device, backend
            )
            for _ in range(num_layers)
        ]
    return kv_caches


def prepare_prefill_model_runner_input(
    config: VllmConfig,
    seq_lens: list[int],
    model_runner: ModelRunner,
):
    block_size = config.cache_config.block_size
    seq_group_metadata_list = build_prefill_seq_group_metadatas(seq_lens, block_size)
    num_blocks = sum([len(sg.block_tables[0]) for sg in seq_group_metadata_list])
    model_input = model_runner.prepare_model_input(seq_group_metadata_list)
    kv_caches = build_kv_caches(config, num_blocks, "FLASH_ATTN")
    return model_input, kv_caches


def prepare_decode_model_runner_input(
    config: VllmConfig,
    seq_lens: list[int],
    model_runner: ModelRunner,
):
    block_size = config.cache_config.block_size
    seq_group_metadata_list = build_decode_seq_group_metadatas(seq_lens, block_size)
    num_blocks = sum([len(sg.block_tables[0]) for sg in seq_group_metadata_list])
    print("num_blocks", num_blocks)
    for seq_group_metadata in seq_group_metadata_list:
        assert seq_group_metadata.token_chunk_size == 1
    model_input = model_runner.prepare_model_input(seq_group_metadata_list)
    kv_caches = build_kv_caches(config, num_blocks, "FLASH_ATTN")
    return model_input, kv_caches

def prepare_hybrid_model_runner_input(
    config: VllmConfig,
    seq_lens: list[int],
    model_runner: ModelRunner,
):
    block_size = config.cache_config.block_size
    seq_group_metadata_list = build_hybrid_seq_group_metadatas(seq_lens, block_size)
    num_blocks = sum([len(sg.block_tables[0]) for sg in seq_group_metadata_list])
    print("num_blocks", num_blocks)
    model_input = model_runner.prepare_model_input(seq_group_metadata_list)
    kv_caches = build_kv_caches(config, num_blocks, "FLASH_ATTN")
    return model_input, kv_caches


class DistributedModelRunner:
    """Wrapper for distributed model execution using MultiprocessingGPUExecutor"""

    def __init__(self, vllm_config: VllmConfig):
        self.vllm_config = vllm_config
        self.executor = None

    def initialize_executor(self):
        """Initialize the multiprocessing executor for TP=4"""

        # Create a custom executor class that inherits from MultiprocessingGPUExecutor
        class ProfilingMultiprocessingGPUExecutor(MultiprocessingGPUExecutor):
            def __init__(self, vllm_config: VllmConfig):

                super().__init__(
                    vllm_config=vllm_config,
                )

        self.executor = ProfilingMultiprocessingGPUExecutor(self.vllm_config)

    def load_model(self):
        """Load model across all workers"""
        if self.executor is None:
            raise RuntimeError(
                "Executor not initialized. Call initialize_executor() first."
            )
        # The executor handles distributed model loading automatically

    def initialize_cache(self, num_gpu_blocks: int, num_cpu_blocks: int):
        """Initialize cache across all workers"""
        if self.executor is None:
            raise RuntimeError(
                "Executor not initialized. Call initialize_executor() first."
            )
        self.executor.initialize_cache(num_gpu_blocks, num_cpu_blocks)

    def execute_model(self, model_input, kv_caches=None):
        """Execute model across distributed workers

        Note: kv_caches parameter is ignored in distributed mode as the executor
        manages caches automatically across workers.
        """
        if self.executor is None:
            raise RuntimeError(
                "Executor not initialized. Call initialize_executor() first."
            )

        # Create ExecuteModelRequest for the executor
        execute_model_req = ExecuteModelRequest(
            seq_group_metadata_list=model_input.seq_group_metadata_list,
            blocks_to_swap_in=[],
            blocks_to_swap_out=[],
            blocks_to_copy=[],
            virtual_engine=0,
            num_lookahead_slots=0,
        )

        # Execute through the distributed executor
        return self.executor.execute_model(execute_model_req)

    def shutdown(self):
        """Shutdown the distributed executor"""
        if self.executor is not None:
            try:
                # Clean shutdown of worker processes
                if (
                    hasattr(self.executor, "worker_monitor")
                    and self.executor.worker_monitor
                ):
                    self.executor.worker_monitor.close()
                if hasattr(self.executor, "workers"):
                    for worker in self.executor.workers:
                        try:
                            if hasattr(worker, "terminate_worker"):
                                worker.terminate_worker()
                        except Exception as e:
                            print(f"Warning: Failed to terminate worker: {e}")
            except Exception as e:
                print(f"Warning: Error during executor shutdown: {e}")
            finally:
                self.executor = None


if __name__ == "__main__":
    # 设定可见 GPU 为 0,1,2,3
    os.environ["CUDA_VISIBLE_DEVICES"] = "0,1,2,3"

    # Configure for TP=4 with Qwen2.5-72B
    engine_args = EngineArgs(
        model="Qwen/Qwen2.5-72B",  # Updated to 72B model
        enforce_eager=True,
        load_format="dummy",
        enable_chunked_prefill=True,
        max_num_batched_tokens=1024,
        tensor_parallel_size=4,  # Updated to TP=4
        max_model_len=4096,
        distributed_executor_backend="mp",  # Use multiprocessing backend
        gpu_memory_utilization=0.85,  # Slightly lower for large model
        max_parallel_loading_workers=4,  # Parallel loading for TP=4
        disable_custom_all_reduce=False,  # Enable custom all-reduce for performance
    )
    config = engine_args.create_engine_config()

    # Initialize distributed model runner
    distributed_runner = DistributedModelRunner(config)
    distributed_runner.initialize_executor()
    distributed_runner.load_model()

    # Initialize cache for distributed execution
    # Calculate cache blocks based on available GPU memory
    num_gpu_blocks = 1000  # Placeholder - in real usage this would be calculated
    num_cpu_blocks = 100  # Placeholder - in real usage this would be calculated
    distributed_runner.initialize_cache(num_gpu_blocks, num_cpu_blocks)

    try:
        # 64 decoding batch
        batch = 64
        decoding_len = 1024
        seq_lens = [decoding_len for _ in range(batch)]

        # Note: For distributed execution, we need to modify the prepare functions
        # to work without a direct ModelRunner reference. For now, we'll use a placeholder
        # approach that creates the inputs without the ModelRunner dependency.

        # Create model inputs for distributed execution
        block_size = config.cache_config.block_size

        # Prepare decoding batch
        decoding_seq_group_metadata_list = build_decode_seq_group_metadatas(
            seq_lens, block_size
        )
        decoding_num_blocks = sum(
            [len(sg.block_tables[0]) for sg in decoding_seq_group_metadata_list]
        )
        decoding_kv_caches = build_kv_caches(config, decoding_num_blocks, "FLASH_ATTN")

        # Create a simple model input object for distributed execution
        class ModelInputForDistributed:
            def __init__(self, seq_group_metadata_list):
                self.seq_group_metadata_list = seq_group_metadata_list

        decoding_model_input = ModelInputForDistributed(
            decoding_seq_group_metadata_list
        )

        # Prepare hybrid batch
        chunk_size = 256
        seq_lens = [decoding_len for _ in range(batch)] + [chunk_size - batch]
        hybrid_seq_group_metadata_list = build_hybrid_seq_group_metadatas(
            seq_lens, block_size
        )
        hybrid_num_blocks = sum(
            [len(sg.block_tables[0]) for sg in hybrid_seq_group_metadata_list]
        )
        hybrid_kv_caches = build_kv_caches(config, hybrid_num_blocks, "FLASH_ATTN")
        hybrid_model_input = ModelInputForDistributed(hybrid_seq_group_metadata_list)

        print("预热模型")
        distributed_runner.execute_model(decoding_model_input, decoding_kv_caches)
        print("完成 decoding batch")
        distributed_runner.execute_model(hybrid_model_input, hybrid_kv_caches)
        print("完成 hybrid batch")

        nvtx_profiler.enable()  # 记录函数栈
        torch.cuda.profiler.start()  # profile 范围开始

        for i in range(2):
            print("执行模型")
            distributed_runner.execute_model(decoding_model_input, decoding_kv_caches)
            distributed_runner.execute_model(hybrid_model_input, hybrid_kv_caches)

        torch.cuda.profiler.stop()  # profile 范围结束
        nvtx_profiler.disable()  # 停止记录函数栈

    except KeyboardInterrupt:
        print("Execution interrupted by user")
    except Exception as e:
        print(f"Error during execution: {e}")
        import traceback

        traceback.print_exc()
    finally:
        # Clean shutdown of distributed resources
        print("Shutting down distributed resources...")
        distributed_runner.shutdown()
        print("Shutdown complete.")
