{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 准备环境\n", "import os\n", "import sys\n", "import json\n", "import matplotlib.pyplot as plt\n", "\n", "if \"..\" not in sys.path:\n", "    sys.path.append(\"..\")\n", "from evaluation_utils.script_generator import (\n", "    ScriptGenerator,\n", "    load_evaluation_conf,\n", "    AttainmentPlotter,\n", "    <PERSON><PERSON>erP<PERSON><PERSON>,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conf = load_evaluation_conf(\"../common_conf.yaml\", \"./conf.yaml\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["script_path = ScriptGenerator(conf).generate_all_scripts()\n", "print(f\"Please run: bash {script_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["AttainmentPlotter(conf).plot()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ScatterPlotter(conf).plot()"]}], "metadata": {"kernelspec": {"display_name": "PD-vllm", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}