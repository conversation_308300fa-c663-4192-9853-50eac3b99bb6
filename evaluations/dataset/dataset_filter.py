# %%
# filter datasets
import os
import sys
from typing import List
import json
import random
from tqdm import tqdm
from pandas import read_parquet
from transformers import PreTrainedTokenizerBase, AutoTokenizer
import matplotlib.pyplot as plt
import numpy as np

from pydantic import BaseModel, model_validator
from typing import List, Tuple, Dict, Any

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from evaluation_utils.conf_loader import load_conf

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from benchmarks.benchmark_serving import TestRequest, TestDataset


class DatasetFilterConf(BaseModel):
    """数据集筛选配置"""

    # 数据集目录
    dataset_dir: str
    # 数据集列表 (数据集名称, 原始文件列表, 过滤后的文件, 最大长度)
    datasets: List[tuple[str, List[str], str, int]]
    selected_datasets: List[int]

    num_prompts: int
    dummy_decoding_length: int

    # 模型配置
    selected_model: int
    models: List[Tuple[str, int]]
    model: str  # 自动生成

    @model_validator(mode="before")
    def extract_model(cls, values):
        values["model"] = values["models"][values["selected_model"]][0]
        return values


def load_dataset_conf(common_conf_path: str, conf_path: str) -> DatasetFilterConf:
    final_conf = load_conf(common_conf_path, conf_path)
    return DatasetFilterConf(**final_conf)


class DatasetFilter:

    def __init__(self, conf: DatasetFilterConf):
        self.conf = conf
        self.tokenizer = AutoTokenizer.from_pretrained(conf.model)

    def filter_all(self):
        for i in self.conf.selected_datasets:
            dataset_name, raw_files, filtered_file, max_len = self.conf.datasets[i]

            raw_paths = [
                os.path.join(self.conf.dataset_dir, raw_file) for raw_file in raw_files
            ]

            filtered_path = os.path.join(self.conf.dataset_dir, filtered_file)
            print("filtering dataset: ", dataset_name)

            if dataset_name == "longbench":
                assert len(raw_paths) == 1
                dataset = self._filter_longbench(
                    raw_paths[0],
                    self.tokenizer,
                    max_len,
                    self.conf.num_prompts,
                )

            elif dataset_name == "sharegpt":
                assert len(raw_paths) == 1
                dataset = self._filter_sharegpt(
                    raw_paths[0],
                    self.tokenizer,
                    max_len,
                    self.conf.num_prompts,
                )

            elif dataset_name == "mixed":
                assert len(raw_paths) > 1
                dataset = self._filter_mixed(
                    raw_paths, self.tokenizer, max_len, self.conf.num_prompts
                )

            elif dataset_name == "dummy":
                dataset = self._generate_dummy_dataset(
                    raw_paths,
                    self.tokenizer,
                    max_len,
                    self.conf.num_prompts,
                    self.conf.dummy_decoding_length,
                )

            elif dataset_name == "arxiv":
                assert len(raw_paths) == 1
                dataset = self._filter_arxiv(
                    raw_paths[0],
                    self.tokenizer,
                    max_len,
                    self.conf.num_prompts,
                )

            elif dataset_name == "openchat":
                assert len(raw_paths) == 1
                dataset = self._filter_openchat(
                    raw_paths[0],
                    self.tokenizer,
                    max_len,
                    self.conf.num_prompts,
                )

            elif dataset_name == "humaneval":
                assert len(raw_paths) == 1
                dataset = self._filter_humaneval(
                    raw_paths[0],
                    self.tokenizer,
                    max_len,
                    self.conf.num_prompts,
                )

            elif dataset_name == "openthoughts":
                assert len(raw_paths) == 1
                dataset = self._filter_openthoughts(
                    raw_paths[0],
                    self.tokenizer,
                    max_len,
                    self.conf.num_prompts,
                )

            else:
                raise ValueError(f"Unsupported dataset name: {dataset_name}")

            self._hist_distribution(dataset)
            dataset.dump(filtered_path)

    def _filter_humaneval(
        self,
        dataset_path: str,
        tokenizer: PreTrainedTokenizerBase,
        max_len: int,
        num_prompts: int,
    ) -> TestDataset:
        filtered_dataset = []
        with open(dataset_path, "r") as f:
            for line in f.readlines():
                if line.strip() == "":
                    continue
                data = json.loads(line)

                context = data["prompt"]
                context_token_ids = tokenizer(context).input_ids
                answer = data["canonical_solution"]
                answer_token_ids = tokenizer(answer).input_ids

                if len(context_token_ids) + len(answer_token_ids) >= max_len:
                    continue

                filtered_dataset.append(
                    TestRequest(
                        context,
                        len(context_token_ids),
                        len(answer_token_ids),
                        "APIKEY_None",
                    )
                )

        # Copy the dataset since it's too small.
        expanded_dataset = []
        while len(expanded_dataset) < num_prompts:
            expanded_dataset.extend(filtered_dataset)
        expanded_dataset = expanded_dataset[:num_prompts]

        return TestDataset("humaneval", expanded_dataset)

    def _filter_longbench(
        self,
        dataset_path: str,
        tokenizer: PreTrainedTokenizerBase,
        max_len: int,
        num_prompts: int,
    ) -> TestDataset:
        # find all .jsonl files under the dataset_path
        files = []
        for root, dirs, filenames in os.walk(dataset_path):
            for filename in filenames:
                if filename.endswith(".jsonl"):
                    files.append(os.path.join(root, filename))

        filtered_requests: List[TestRequest] = []
        with tqdm(total=num_prompts) as progress_bar:
            for file in files:
                with open(file, "r") as f:
                    for line in f.readlines():
                        if len(filtered_requests) >= num_prompts:
                            break
                        if line.strip() == "":
                            continue
                        data = json.loads(line)

                        # skip if the output is too short
                        answer_token_ids = tokenizer(data["answers"][0]).input_ids
                        answer_len = len(answer_token_ids)
                        if answer_len < 8:
                            continue

                        context = data["context"][:40000]  # truncate to reduce time
                        context_token_ids = tokenizer(context).input_ids
                        context_len = len(context_token_ids)

                        # truncate context if it's too long
                        context_len_allowed = min(
                            max_len - 8 - answer_len,
                            random.randint(int(max_len * 0.9), max_len),
                        )
                        context_token_ids = context_token_ids[:context_len_allowed]

                        # 构建请求
                        request = TestRequest(
                            tokenizer.decode(context_token_ids),
                            len(context_token_ids),
                            answer_len,
                            "APIKEY_Summarization",
                        )
                        filtered_requests.append(request)
                        progress_bar.update(1)

        return TestDataset("longbench", filtered_requests)

    # sharegpt 筛选程序
    def _filter_sharegpt(
        self,
        dataset_path: str,
        tokenizer: PreTrainedTokenizerBase,
        max_len: int,
        num_prompts: int,
    ) -> TestDataset:
        # Load the dataset.
        if not os.path.exists(dataset_path):
            raise FileNotFoundError(f"Dataset path {dataset_path} does not exist.")
        with open(dataset_path, "r") as f:
            dataset = json.load(f)

        filtered_requests: List[TestRequest] = []
        with tqdm(total=num_prompts) as progress_bar:
            for data in dataset:
                if len(filtered_requests) >= num_prompts:
                    break

                num_conversations = len(data["conversations"])
                if num_conversations < 2:
                    # Prune the conversations with less than 2 turns.
                    continue

                # random the turns
                num_prompt_turns = random.randint(1, num_conversations - 1)
                # num_prompt_turns = num_conversations - 1

                prompt = "\n".join(
                    [data["conversations"][i]["value"] for i in range(num_prompt_turns)]
                )
                completion = data["conversations"][num_prompt_turns]["value"]
                prompt_token_ids = tokenizer(prompt).input_ids
                completion_token_ids = tokenizer(completion).input_ids

                prompt_len = len(prompt_token_ids)
                output_len = len(completion_token_ids)
                if prompt_len < 10 or output_len < 10:
                    # Prune too short sequences.
                    continue
                if prompt_len + output_len >= max_len:
                    # Prune too long sequences.
                    continue
                # if output_len > 500:
                #     # Prune too long output sequences.
                #     continue

                request = TestRequest(
                    prompt,
                    prompt_len,
                    output_len,
                    "APIKEY_Chatbot",
                )
                filtered_requests.append(request)
                progress_bar.update(1)

        return TestDataset(f"sharegpt", filtered_requests)

    def _filter_mixed(
        self,
        raw_paths: List[str],
        tokenizer: PreTrainedTokenizerBase,
        max_len: int,
        num_prompts: int,
    ) -> TestDataset:
        # 混合筛选, 从 raw_paths 中的每个 TestDataset 中随机选取 num_prompts 个请求, 其概率保持相同

        # Load the datasets.
        datasets = [TestDataset.load(path) for path in raw_paths]
        requests = []
        ratios = [0.75, 0.25]
        for dataset, ratio in zip(datasets, ratios):
            num_requests = int(num_prompts * ratio)
            dataset_requests = dataset.requests[:num_requests]
            requests.extend(dataset_requests)
        random.shuffle(requests)
        return TestDataset("mixed", requests)

    def _filter_arxiv(
        self,
        dataset_path: str,
        tokenizer: PreTrainedTokenizerBase,
        max_len: int,
        num_prompts: int,
    ) -> TestDataset:
        # Load the dataset.
        if not os.path.exists(dataset_path):
            raise FileNotFoundError(f"Dataset path {dataset_path} does not exist.")
        dataset = read_parquet(dataset_path)

        print(dataset.columns)

        filtered_requests: List[TestRequest] = []
        with tqdm(total=num_prompts) as progress_bar:
            for _, data in dataset.iterrows():
                if len(filtered_requests) >= num_prompts:
                    break
                prompt = data["article"]
                completion = data["abstract"]

                prompt_token_ids = tokenizer(prompt).input_ids
                completion_token_ids = tokenizer(completion).input_ids

                prompt_len = len(prompt_token_ids)
                output_len = len(completion_token_ids)
                if prompt_len < 10 or output_len < 10:
                    # Prune too short sequences.
                    continue
                if prompt_len + output_len >= max_len:
                    # Prune too long sequences.
                    continue

                request = TestRequest(
                    prompt,
                    prompt_len,
                    output_len,
                    "APIKEY_Summarization",
                )
                filtered_requests.append(request)
                progress_bar.update(1)

        return TestDataset("arxiv", filtered_requests)

    def _filter_openchat(
        self,
        dataset_path: str,
        tokenizer: PreTrainedTokenizerBase,
        max_len: int,
        num_prompts: int,
    ) -> TestDataset:
        # 载入 json 文件格式的数据
        if not os.path.exists(dataset_path):
            raise FileNotFoundError(f"Dataset path {dataset_path} does not exist.")
        with open(dataset_path, "r") as f:
            dataset = json.load(f)

        filtered_requests: List[TestRequest] = []
        with tqdm(total=num_prompts) as progress_bar:
            for data in dataset:
                if len(filtered_requests) >= num_prompts:
                    break

                last_pos = data.rfind("Assistant:")

                prompt = data[:last_pos]
                completion = data[last_pos:]
                prompt_token_ids = tokenizer(prompt).input_ids
                completion_token_ids = tokenizer(completion).input_ids

                prompt_len = len(prompt_token_ids)
                output_len = len(completion_token_ids)
                if prompt_len < 10 or output_len < 10:
                    # Prune too short sequences.
                    continue
                if prompt_len + output_len >= max_len:
                    # Prune too long sequences.
                    continue

                request = TestRequest(
                    prompt,
                    prompt_len,
                    output_len,
                    "APIKEY_Chatbot",
                )
                filtered_requests.append(request)
                progress_bar.update(1)

        return TestDataset(f"openchat", filtered_requests)

    def _filter_openthoughts(
        self,
        dataset_path: str,
        tokenizer: PreTrainedTokenizerBase,
        max_len: int,
        max_prompts: int,
    ) -> TestDataset:
        # Load the dataset.
        ds = read_parquet(dataset_path)
        dataset = ds[:max_prompts]

        filtered_requests: List[TestRequest] = []
        with tqdm(total=max_prompts) as progress_bar:
            for sys_prompt, conversations in zip(
                dataset["system"], dataset["conversations"]
            ):
                num_conversations = len(conversations)
                if num_conversations < 2:
                    continue
                turn_index = random.randint(0, num_conversations // 2 - 1)
                prompt_idx = turn_index * 2
                completion_idx = prompt_idx + 1
                prompt = sys_prompt + conversations[prompt_idx]["value"]
                completion = conversations[completion_idx]["value"]
                prompt_token_ids = tokenizer(prompt).input_ids
                completion_token_ids = tokenizer(completion).input_ids
                prompt_len = len(prompt_token_ids)
                output_len = len(completion_token_ids)
                role = conversations[completion_idx]["from"]

                if prompt_len + output_len > max_len:
                    # Prune too long sequences.
                    output_len = max_len - prompt_len
                if prompt_len < 10 or output_len < 10:
                    # Prune too short sequences.
                    continue
                if role != "assistant":
                    print(f"{turn_index=}, {prompt_idx=}, {completion_idx=}")
                    continue

                request = TestRequest(
                    prompt,
                    prompt_len,
                    output_len,
                    "APIKEY_None",
                )
                filtered_requests.append(request)
                progress_bar.update(1)

        return TestDataset("openthoughts", filtered_requests)

    def _generate_dummy_dataset(
        self,
        raw_paths: List[str],
        tokenizer: PreTrainedTokenizerBase,
        max_len: int,
        num_prompts: int,
        dummy_decoding_length: int,
    ) -> TestDataset:
        input_len, output_len = max_len, dummy_decoding_length

        def random_tokens(length: int):
            return np.random.randint(0, tokenizer.vocab_size, size=length).tolist()

        requests = []
        for i in tqdm(range(num_prompts)):
            prompt_tokens = random_tokens(input_len)
            request = TestRequest(
                tokenizer.decode(prompt_tokens),
                input_len,
                output_len,
                "APIKEY_None",
            )

            requests.append(request)

        return TestDataset("dummy", requests)

    def _hist_distribution(self, dataset: TestDataset):
        plt.rcParams.update({"font.size": 18})

        prompt_lens = [r.prompt_len for r in dataset.requests]
        output_lens = [r.output_len for r in dataset.requests]
        print("cached_data:")
        print((prompt_lens, output_lens))

        avg_prompt_len = sum(prompt_lens) / len(prompt_lens)
        avg_output_len = sum(output_lens) / len(output_lens)
        max_len = max(max(prompt_lens), max(output_lens))

        plt.hist(
            prompt_lens,
            bins=100,
            range=(0, max_len),
            alpha=0.5,
            label=f"Input: avg={avg_prompt_len:.1f}",
        )

        plt.hist(
            output_lens,
            bins=100,
            range=(0, max_len),
            alpha=0.5,
            label=f"Output: avg={avg_output_len:.1f}",
        )

        plt.legend(loc="upper right")

        os.makedirs(".cache", exist_ok=True)
        figure_path = os.path.join(".cache", f"{dataset.dataset_name}_hist.pdf")
        figure_path = os.path.abspath(figure_path)
        plt.savefig(figure_path)
        print(f"Saving distribution histogram to {figure_path}")

        plt.show()
        plt.cla()


if __name__ == "__main__":
    current_dir = os.path.dirname(os.path.abspath(__file__))
    common_conf_path = os.path.join(current_dir, "../common_conf.yaml")
    conf_path = os.path.join(current_dir, "conf.yaml")

    conf = load_dataset_conf(common_conf_path, conf_path)

    # 创建 DatasetFilter 对象
    dataset_filter = DatasetFilter(conf)

    # 执行过滤
    dataset_filter.filter_all()

# %%
