#!/bin/bash
# Note:
# 1. configure the dataset path
# 2. configure the wget proxy if needed

DATASET_DIR="/root/dataset/"

mkdir -p $DATASET_DIR/raw
cd $DATASET_DIR/raw

# Download the "ShareGPT" dataset
wget https://huggingface.co/datasets/anon8231489123/ShareGPT_Vicuna_unfiltered/resolve/main/ShareGPT_V3_unfiltered_cleaned_split.json

# Download the "HumanEval" dataset
wget https://github.com/openai/human-eval/raw/master/data/HumanEval.jsonl.gz
gunzip HumanEval.jsonl.gz

# Download the "LongBench" dataset
wget "https://huggingface.co/datasets/THUDM/LongBench/resolve/main/data.zip?download=true" -O longbench.zip
unzip longbench.zip
mv data longbench

# Download the "OpenThoughts-114k" dataset
wget https://huggingface.co/datasets/open-thoughts/OpenThoughts-114k/resolve/main/data/train-00000-of-00006.parquet\?download\=true -O train-00000-of-00006.parquet

# Download arxiv summarization dataset
wget https://huggingface.co/datasets/ccdv/arxiv-summarization/resolve/main/section/test-00000-of-00001.parquet\?download\=true -O arxiv.parquet

# Download the "OpenChat" dataset
wget https://huggingface.co/datasets/openchat/openchat_sharegpt4_dataset/resolve/main/openchat_8192.train.text.json?download=true -O openchat_8192.train.text.json