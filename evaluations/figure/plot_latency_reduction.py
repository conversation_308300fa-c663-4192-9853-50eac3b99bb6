# %%
import dataclasses
import itertools
import os
import matplotlib.pyplot as plt
import numpy

from matplotlib.ticker import MultipleLocator
from pathlib import Path
from typing import Optional
from plot_utils import (
    crop_margins,
    get_deduplicate_legends,
    SYS_NAME,
    PD_COLOR,
    CP_COLOR,
    HYBRID_COLOR,
)


@dataclasses.dataclass
class PlotConfig:
    figsize = (5, 2)
    bar_width = 0.20
    bar_padding = 1.4
    colors = ["tab:blue", "tab:orange", "tab:green", "tab:gray"]
    normalized_line_color = "red"
    dataset_space = 0.5


# 原始数据
# data =  workload_name -> ((ttft_slo, tpot_slo), list of (scheduler, p90_ttft, p90_tpot))
data: dict[str, tuple[tuple[float, float], list[tuple[str, float, float]]]] = {
    "14b_sharegpt": ((3.0, 0.11), [('hybrid', 2.5301613565534353, 0.09974478438215435), ('pd_disaggregation', 12.11991417966783, 0.046796697465812456), ('chunked_prefill', 1.2618560995906591, 0.11983364172603773)]),
    "32b_sharegpt": ((3.0, 0.12), [('hybrid', 1.9763928279280663, 0.11542344383895398), ('pd_disaggregation', 14.782125759869814, 0.048665099438998195), ('chunked_prefill', 1.0659345798194408, 0.12791356420063454)]),
    "14b_sharegpt_tpot": ((4.0, 0.07), [('hybrid', 2.9732513148337603, 0.06705548109427567), ('pd_disaggregation', 8.711573531851172, 0.049359294565001946), ('chunked_prefill', 1.5048696845769882, 0.08280851133167744)]),
    "32b_sharegpt_tpot": ((4.0, 0.08), [('hybrid', 3.399503830820322, 0.07591716205682668), ('pd_disaggregation', 12.15825398825109, 0.04843071158975363), ('chunked_prefill', 1.5123477708548307, 0.09196356438821363)]),
    "14b_arxiv": ((4.0, 0.07), [('hybrid', 3.892180820927024, 0.06251185067952418), ('pd_disaggregation', 51.364911964163184, 0.02945178909296239), ('chunked_prefill', 2.849537219852209, 0.10017143493020757)]),
    "32b_arxiv": ((4.0, 0.07), [('hybrid', 3.5084574203938246, 0.057398652641085894), ('pd_disaggregation', 13.827013546600938, 0.03901452517532038), ('chunked_prefill', 2.9389824997633696, 0.07937356350200059)]),
    "14b_arxiv_tpot": ((6.0, 0.05), [('hybrid', 5.508928339928389, 0.04385835147418436), ('pd_disaggregation', 27.061027076095343, 0.02936744811013341), ('chunked_prefill', 3.1997800786048174, 0.07403470995712143)]),
    "32b_arxiv_tpot": ((6.0, 0.06), [('hybrid', 5.7401810195297, 0.05082722697031978), ('pd_disaggregation', 13.917341472581029, 0.038615404536911084), ('chunked_prefill', 3.207828227430582, 0.07093754704845578)]),
}


def get_improvement(results: list[list[tuple[str, float, float]]]) -> None:
    """
    Get the improvement of each method compared to the hybrid method.
    """
    ttft_improvement = []
    tpot_improvement = []
    ours = results[0]
    for result in results:
        for method, ttft, tpot in result[1:]:
            ttft_improvement.append(ttft / results[0][1])
            tpot_improvement.append(tpot / results[0][2])

    print(f"ttft_improvement: {ttft_improvement}")
    print(f"tpot_improvement: {tpot_improvement}")


def label_for_display(label: str):
    lable_map = {
        "hybrid": SYS_NAME,
        "pd_disaggregation": "PD-Disaggregation",
        "chunked_prefill": "PD-Aggregation",
    }
    return lable_map[label]


def xtick_for_display(xtick: str):
    xticks_map = {
        "14b_sharegpt": "14B\nSLO1",
        "32b_sharegpt": "32B\nSLO1",
        "14b_sharegpt_tpot": "14B\nSLO2",
        "32b_sharegpt_tpot": "32B\nSLO2",
        "14b_arxiv": "14B\nSLO1",
        "32b_arxiv": "32B\nSLO1",
        "14b_arxiv_tpot": "14B\nSLO2",
        "32b_arxiv_tpot": "32B\nSLO2",
    }
    return xticks_map[xtick]


def plot_latency_metric(
    latencies: dict[tuple[str, str], float],
    configs: list[str],
    categories: list[str],
    # normalized with slo under different configs
    slos: Optional[dict[str, float]] = None,
    save_path: str = "latency.pdf",
    width: float = PlotConfig.bar_width,
    bar_padding: float = PlotConfig.bar_padding,
    colors: list[str] = PlotConfig.colors,
    y_top_lim: Optional[float] = None,
    major_locator: Optional[float] = None,
    with_legend: bool = True,
    is_crop_margins: bool = False,
):
    assert len(colors) >= len(categories)
    fig, ax = plt.subplots(figsize=PlotConfig.figsize)
    x_bases = numpy.array(
        [i + PlotConfig.dataset_space if i >= len(configs) / 2 else i for i in range(len(configs))]
    )
    x_offsets = numpy.arange(len(categories)) * width - width * (len(categories) - 1) / 2
    x_offsets = x_offsets * bar_padding
    if slos is not None:
        assert len(slos) == len(configs)
        latencies = {
            (config, category): latencies[(config, category)] / slos[config]
            for config in configs
            for category in categories
        }
        ax.axhline(
            1,
            linestyle="--",
            color=PlotConfig.normalized_line_color,
            linewidth=3,
        )

    for (cfg_idx, config), (cat_idx, category) in itertools.product(
        enumerate(configs), enumerate(categories)
    ):
        x_position = x_bases[cfg_idx] + x_offsets[cat_idx]
        latency = latencies[(config, category)]
        ax.bar(
            x_position,
            latency,
            width,
            label=label_for_display(category),
            color=colors[cat_idx],
            edgecolor="black",
            linewidth=0.5,
        )
        if y_top_lim and latency > y_top_lim:
            ax.text(x_position, y_top_lim * 1.02, f"{latency:.2f}", ha="center")

    if y_top_lim is not None:
        ax.set_ylim(top=y_top_lim)
    ax.axvline((x_bases[0] + x_bases[-1]) / 2, color="gray")
    if y_top_lim is not None:
        dataset_y_pos = y_top_lim
    else:
        dataset_y_pos = max(latencies.values())
    dataset0_x_pos = (x_bases[0] + x_bases[3]) / 2
    dataset1_x_pos = (x_bases[4] + x_bases[7]) / 2
    ax.text(dataset0_x_pos, dataset_y_pos * 0.85, f"sharegpt", ha="center")
    ax.text(dataset1_x_pos, dataset_y_pos * 0.85, f"arxiv", ha="center")

    if major_locator is not None:
        ax.yaxis.set_major_locator(MultipleLocator(major_locator))
        ax.yaxis.set_minor_locator(MultipleLocator(major_locator / 5))

    xticks = [xtick_for_display(config) for config in configs]
    ax.grid(axis="y", color="lightgray", linestyle="--", linewidth=0.5)
    ax.set_xticks(x_bases, xticks)
    ax.set_axisbelow(True)
    if with_legend:
        handles, labels = get_deduplicate_legends(fig)
        fig.legend(handles, labels, ncol=3, loc="outside upper center", frameon=False)
        fig.tight_layout()
        fig.subplots_adjust(top=0.78)
    else:
        fig.tight_layout()
    os.makedirs(Path(save_path).parent, exist_ok=True)
    fig.savefig(save_path)
    if is_crop_margins:
        crop_margins(save_path)


def plot_latency_legend(save_path: str = "legend.pdf"):
    plot_latency_metric(
        p90_ttfts,
        configs,
        categories,
        ttft_slos,
        save_path=save_path,
        y_top_lim=6,
        major_locator=1,
        with_legend=True,
    )
    crop_margins(save_path, "-p4 10 -830 10 10")


if __name__ == "__main__":
    configs = list(data.keys())
    methods = [t[0] for t in data[configs[0]][1]]
    color_map = {
        "pd_disaggregation": PD_COLOR,
        "chunked_prefill": CP_COLOR,
        "hybrid": HYBRID_COLOR,
    }
    categories = ["chunked_prefill", "pd_disaggregation", "hybrid"]
    colors = [color_map[category] for category in categories]
    assert all([category in methods for category in categories])

    p90_ttfts = {
        (config, t[0]): t[1]
        for config, value in data.items()
        for t in value[1]
    }
    ttft_slos = {config: data[config][0][0] for config in configs}
    plot_latency_metric(
        p90_ttfts,
        configs,
        categories,
        ttft_slos,
        save_path=".cache/ttft.pdf",
        colors=colors,
        y_top_lim=6,
        major_locator=1,
        with_legend=True,
        is_crop_margins=True,
    )

    p90_tpots = {
        (config, t[0]): t[2]
        for config, value in data.items()
        for t in value[1]
    }
    tpot_slos = {config: data[config][0][1] for config in configs}
    plot_latency_metric(
        p90_tpots,
        configs,
        categories,
        tpot_slos,
        save_path=".cache/tpot.pdf",
        colors=colors,
        y_top_lim=2,
        major_locator=0.5,
        with_legend=True,
        is_crop_margins=True,
    )

    plot_latency_legend(save_path=".cache/latency_legend.pdf")

    # get_improvement(data)
    get_improvement([data[key][1] for key in data.keys()])
