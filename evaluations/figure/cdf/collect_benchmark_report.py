#!/usr/bin/env python3
"""
从指定文件中读取 "Full report_data: json string" 格式的数据，
并保存到 .cache/data 目录下
"""

import argparse
import json
import os
import re
import sys
from pathlib import Path


def extract_report_data(file_path):
    """
    从文件中提取 "Full report_data: json string" 格式的数据
    
    Args:
        file_path (str): 输入文件路径
        
    Returns:
        Any: 提取到的JSON数据列表
    """
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 使用正则表达式匹配 "Full report_data: " 后面的JSON字符串
        pattern = r'Full report_data:\s*(.+?)(?=\n|$)'
        matches = re.findall(pattern, content, re.MULTILINE)
        assert len(matches) == 1, f"Expected 1 match, got {len(matches)}"
        match = matches[0]
        
        try: 
            json_data = json.loads(match.strip())
            return json_data
        except json.JSONDecodeError as e:
            print(f"警告: 无法解析JSON数据: {match[:50]}... 错误: {e}")
            return {"error": f"JSONDecodeError: {e}"}
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 读取文件时出现问题: {e}")
        sys.exit(1)
        
    return {"error": f"UnknownError: {e}"}


def save_data_to_cache(data, output_filename, script_dir):
    """
    将数据保存到 .cache/data 目录下

    Args:
        data (list): 要保存的数据
        output_filename (str): 输出文件名
        script_dir (str): 脚本所在目录
    """
    # 创建 .cache/data 目录
    cache_data_dir = os.path.join(script_dir, '.cache', 'data')
    os.makedirs(cache_data_dir, exist_ok=True)

    # 构建输出文件路径
    output_path = os.path.join(cache_data_dir, output_filename)
    
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 数据已保存到: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"错误: 保存文件时出现问题: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description='从 Benchmark report log 文件中收集 "Full report_data: json string" 格式的数据并保存到 .cache/data 目录'
    )
    
    parser.add_argument(
        'input_file',
        help='输入文件路径'
    )
    
    parser.add_argument(
        '-o', '--output',
        default='collected_data.json',
        help='输出文件名 (默认: collected_data.json)'
    )
    
    args = parser.parse_args()
    
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    print(f"📖 从文件读取数据: {args.input_file}")
    
    # 提取数据
    extracted_data = extract_report_data(args.input_file)
    
    if not extracted_data:
        print("⚠️  未找到任何 'Full report_data:' 格式的数据")
        sys.exit(0)
    
    
    # 保存数据
    output_path = save_data_to_cache(extracted_data, args.output, script_dir)
    
    print("完成")


if __name__ == '__main__':
    main()
