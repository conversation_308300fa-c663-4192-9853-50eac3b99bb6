# %%
# 引入模块
import sys
import json
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.ticker import AutoMinorLocator

# Add parent directory to path to import plot_utils
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
from plot_utils import crop_margins

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10


# %%
# 数据接口 - 基于 plot_cdf.py 的数据读取方式
def get_data_with_length(file_path: str, data_type: str, length_type: str) -> tuple[list[float], list[float], list[int]]:
    """
    从JSON文件中获取数据并计算CDF，同时获取长度信息
    Args:
        file_path: JSON文件路径
        data_type: 数据类型，可以是 'ttfts' 或 'tpots'
        length_type: 长度数据类型，对应的长度字段
    Returns:
        tuple: (数据值列表, CDF值列表, 平均长度列表)
    """
    with open(file_path, 'r') as f:
        data = json.load(f)

    # 获取指定类型的数据
    assert data_type in data, f"Invalid data type: {data_type}, available types: {data.keys()}"
    assert length_type in data, f"Invalid length type: {length_type}, available types: {data.keys()}"

    values = data[data_type]
    lengths = data[length_type]

    # 确保数据长度一致
    assert len(values) == len(lengths), f"Data length mismatch: {len(values)} vs {len(lengths)}"

    # 如果是TPOT数据，转换为毫秒
    if data_type == 'tpots':
        values = [v * 1000 for v in values]  # 秒转毫秒

    # 创建DataFrame便于处理
    df = pd.DataFrame({
        'values': values,
        'lengths': lengths
    })
    
    # 按值排序
    df = df.sort_values(by='values').reset_index(drop=True)
    
    # 计算CDF
    df['cdf'] = df['values'].rank(method='max', pct=True)
    
    # 将数据分成5组，计算每组的平均长度
    group_size = len(df) // 5
    avgs_list = []
    for i in range(5):
        start_idx = i * group_size
        if i == 4:  # 最后一组包含所有剩余数据
            end_idx = len(df)
        else:
            end_idx = (i + 1) * group_size
        
        group_avg = int(df.iloc[start_idx:end_idx]['lengths'].mean())
        avgs_list.append(group_avg)
    
    x = df['values'].values.tolist()
    y = df['cdf'].values.tolist()
    
    return x, y, avgs_list


class CDFLengthPlotter:
    def __init__(self, data: tuple[list[float], list[float], list[int]], slo: float, file_name: str, data_type: str = "tpots") -> None:
        self.x = data[0]
        self.y = data[1]
        self.texts = data[2]
        self.slo = slo
        self.file_name = file_name
        self.data_type = data_type
        # 判断是否为TPOT数据
        self.is_tpot = data_type == "tpots"

    def plot(self) -> None:
        _, ax = plt.subplots(figsize=(3.3, 2))
        plt.rcParams.update({'font.size': 10})
        print(f'{len(self.x)=}')
        print(f'{self.texts=}')
        plt.plot(self.x, self.y, color="tab:blue")

        # 根据数据类型设置标签和范围
        if self.is_tpot:
            plt.xlabel("TPOT (ms)")
            xlim_max = 400  # 100ms
        else:
            plt.xlabel("TTFT (s)")
            xlim_max = 35   # 35s

        plt.ylabel("CDF")

        plt.xlim(0, max(max(self.x)*1.1, self.slo * 1.1))
        plt.ylim(0, 1.00)
        plt.xlim(0, xlim_max)

        # y 轴 tick 间隔为 0.2
        plt.yticks(np.arange(0, 1.01, 0.20))

        ####### Method 2 - 添加长度注释 ##########
        # 动态计算每个分位数对应的x值（该分段的最大x值）
        n = len(self.x)
        percentiles = [0.2, 0.4, 0.6, 0.8, 1.0]
        # 保持原有的纵坐标位置
        y_positions = [0.2, 0.4, 0.6, 0.8, 1.0]

        for i, (percentile, y_pos, avg_len) in enumerate(zip(percentiles, y_positions, self.texts)):
            # 找到对应分位数的索引
            idx = int(percentile * n) - 1
            if idx >= n:
                idx = n - 1

            # 获取该分位数对应的x值
            x_pos = self.x[idx] * 1.02

            # 计算标注位置（在x_pos右侧一定距离）
            x_offset = xlim_max * 0.05  # 偏移量为x轴范围的5%
            text_x = x_pos + x_offset

            # 绘制水平虚线
            if i > 0:  # 不为第一个分位数绘制虚线
                prev_idx = int(percentiles[i-1] * n) - 1
                if prev_idx >= n:
                    prev_idx = n - 1
                prev_x_pos = self.x[prev_idx]
                plt.axhline(y=y_positions[i-1], xmin=0, xmax=prev_x_pos/xlim_max,
                           color='lightgray', linestyle='--', lw=0.8)

            # 绘制箭头指向分位数点
            ax.annotate('', xy=(x_pos, y_pos-0.1), xytext=(text_x, y_pos-0.1),
                arrowprops=dict(arrowstyle='-[, widthB=0.85, lengthB=0.3', lw=1.0),
                annotation_clip=False)

            # 添加文本标注
            plt.text(text_x, y_pos - 0.13, f"Avg len: {str(avg_len)}", fontsize=9)

        # SLO 注释位置需要根据实际的 SLO 值来设置
        slo_x = self.slo
        if self.is_tpot:
            # 对于 TPOT 数据，SLO 值是毫秒单位
            slo_display_x = slo_x * 1000
        else:
            # 对于 TTFT 数据，SLO 值是秒单位
            slo_display_x = slo_x

        plt.annotate("SLO",
                     xy=(slo_display_x, 1.0),  # 注释的坐标位置指向 SLO 线
                     xytext=(slo_display_x * 0.2, 0.8),  # 文本的显示位置
                     arrowprops=dict(facecolor='black', arrowstyle="->"))  # 箭头样式

        ax.yaxis.set_minor_locator(AutoMinorLocator(5))
        ax.xaxis.set_minor_locator(AutoMinorLocator(5))

        # 添加 SLO 线
        plt.axvline(slo_display_x, color='red', linestyle='--')
        plt.tight_layout()

        # 确保输出目录存在 - 保存到当前文件所在目录的.cache下
        current_dir = os.path.dirname(__file__)
        output_dir = os.path.join(current_dir, ".cache")
        os.makedirs(output_dir, exist_ok=True)
        save_path = os.path.join(output_dir, f"{self.file_name}.pdf")
        plt.savefig(save_path)
        plt.show()

        plt.close()
        plt.clf()

        output_path = crop_margins(save_path)
        print(f"Cropped to {output_path} successfully.")


# %%
# 执行
if __name__ == "__main__":
    # 数据文件路径 - 使用与 plot_cdf.py 相同的数据源
    data_file = os.path.join(os.path.dirname(__file__), ".cache", "data", "collected_data.json")
    
    # 定义绘图参数
    # 格式: (数据类型, 长度类型, SLO值, 输出文件名)
    inputs = [
        (
            "tpots",                    # 数据类型
            "output_lengths",           # 长度数据类型（输出长度）
            0.1,                        # SLO值 (0.1秒)
            "tpot_output_length_cdf",   # 输出文件名
        ),
        (
            "ttfts",                    # 数据类型
            "input_lengths",            # 长度数据类型（输入长度）
            5.0,                        # SLO值 (5秒)
            "ttft_input_length_cdf",    # 输出文件名
        ),
    ]

    # 生成绘图数据
    plot_inputs = []
    for (data_type, length_type, slo, file_name) in inputs:
        try:
            data = get_data_with_length(data_file, data_type, length_type)
            plot_inputs.append((data, slo, file_name, data_type))
        except Exception as e:
            print(f"Error loading data for {file_name}: {e}")
            continue

    # 画图
    for data, slo, file_name, data_type in plot_inputs:
        try:
            plotter = CDFLengthPlotter(data, slo, file_name, data_type)
            plotter.plot()
        except Exception as e:
            print(f"Error plotting {file_name}: {e}")


# %%
