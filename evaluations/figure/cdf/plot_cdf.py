# %%
# 引入模块
import sys
import json
import os
import dataclasses
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator
from matplotlib.ticker import AutoMinorLocator

from typing import Any

# Add parent directory to path to import plot_utils
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
from plot_utils import crop_margins

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10


# %%
# 数据接口
def get_data(file_path: str, data_type: str) -> tuple[list[float], list[float]]:
    """
    从JSON文件中获取数据并计算CDF
    Args:
        file_path: JSON文件路径
        data_type: 数据类型，可以是 'ttfts' 或 'tpots'
    Returns:
        tuple: (数据值列表, CDF值列表)
    """
    with open(file_path, 'r') as f:
        data = json.load(f)

    # 获取指定类型的数据
    assert data_type in data, f"Invalid data type: {data_type}, available types: {data.keys()}"

    values = data[data_type]

    # 如果是TPOT数据，转换为毫秒
    if data_type == 'tpots':
        values = [v * 1000 for v in values]  # 秒转毫秒

    # 排序数据
    sorted_values = sorted(values)

    # 计算CDF
    n = len(sorted_values)
    cdf_values = [(i + 1) / n for i in range(n)]

    return sorted_values, cdf_values


# %%
# 定义画图类
class CDFPlotter:
    def __init__(self, data: tuple[list[float], list[float]],
                 slo: float, xlim: float, x_major: float,
                 anno_text: tuple[float, float],
                 anno_point: tuple[float, float],
                 x_title: str,
                 file_name: str
        ) -> None:
        self.x = data[0]
        self.y = data[1]
        self.slo = slo
        self.xlim = xlim
        self.x_major = x_major
        self.anno_text = anno_text
        self.anno_point = anno_point
        self.x_title = x_title
        self.file_name = file_name
        # 判断是否为TPOT数据（毫秒单位）
        self.is_tpot = "TPOT" in x_title

    def plot(self) -> None:
        fig, ax = plt.subplots(figsize=(3, 2))
        plt.rcParams.update({'font.size': 10})
        plt.plot(self.x, self.y, color="tab:blue")
        plt.xlabel(self.x_title)
        plt.ylabel("CDF")

        plt.xlim(0, self.xlim)
        plt.ylim(0, 1)

        # 根据数据类型显示不同的单位
        slo_unit = "ms" if self.is_tpot else "s"
        plt.axvline(self.slo, color='red', linestyle='--', label=f"SLO: {self.slo}{slo_unit}")
        plt.tight_layout()

        ax.yaxis.set_major_locator(MultipleLocator(0.2))
        ax.xaxis.set_major_locator(MultipleLocator(self.x_major))

        ax.yaxis.set_minor_locator(AutoMinorLocator(5))
        ax.xaxis.set_minor_locator(AutoMinorLocator(5))

        plt.grid(axis='y', color='lightgray', linestyle='--')
        plt.grid(axis='x', color='lightgray', linestyle='--')
        ax.set_axisbelow(True)

        plt.annotate("SLO",
                     xy=self.anno_point,  # 注释的坐标位置
                     xytext=self.anno_text,  # 文本的显示位置
                     arrowprops=dict(facecolor='black', arrowstyle="->"))  # 箭头样式

        # 确保输出目录存在 - 保存到当前文件所在目录的.cache 下
        current_dir = os.path.dirname(__file__)
        output_dir = os.path.join(current_dir, ".cache")
        os.makedirs(output_dir, exist_ok=True)
        save_path = os.path.join(output_dir, f"{self.file_name}.pdf")
        plt.savefig(save_path)
        plt.show()

        plt.close()
        plt.clf()

        output_path = crop_margins(save_path)
        print(f"Cropped to {output_path} successfully.")

# %%
# 执行
if __name__ == "__main__":
    # 数据文件路径
    data_file = os.path.join(os.path.dirname(__file__), ".cache", "data", "collected_data.json")
    
    # 定义绘图参数
    # 格式: (数据类型, SLO值, x轴最大值, x轴主刻度间隔, 注释文本位置, 注释箭头指向位置, x轴标题, 输出文件名)
    inputs = [
        (
            "ttfts",      # 数据类型
            5.0,          # SLO值 (假设TTFT的SLO是5秒)
            35,           # x轴最大值
            5,            # x轴主刻度间隔
            (20, 0.2),    # 注释文本位置
            (5.0, 0),     # 注释箭头指向位置
            "TTFT (s)",   # x轴标题
            "collected_ttft_cdf",  # 输出文件名
        ),
        (
            "tpots",      # 数据类型
            50,           # SLO值 (0.05秒 = 50毫秒)
            100,          # x轴最大值 (0.100秒 = 100毫秒)
            20,           # x轴主刻度间隔 (20毫秒)
            (70, 0.2),    # 注释文本位置
            (50, 0),      # 注释箭头指向位置
            "TPOT (ms)",  # x轴标题
            "collected_tpot_cdf",  # 输出文件名
        ),
    ]

    # 生成绘图数据
    plot_inputs = [
        (get_data(data_file, data_type), slo, xlim, x_major, anno_text, anno_point, x_title, file_name)
        for (data_type, slo, xlim, x_major, anno_text, anno_point, x_title, file_name) in inputs
    ]

    # 画图
    for (data, slo, xlim, x_major, anno_text, anno_point, x_title, file_name) in plot_inputs:
        try:
            plotter = CDFPlotter(data, slo, xlim, x_major, anno_text, anno_point, x_title, file_name)
            plotter.plot()
        except Exception as e:
            print(f"Error plotting {file_name}: {e}")


# %%
