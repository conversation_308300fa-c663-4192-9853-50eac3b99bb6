#!/usr/bin/env python3
"""
Academic-style plotting program for latency breakdown performance metrics analysis.

This program processes summary_statistics.json files from different QPS directories
and creates a stacked percentage chart showing the relative proportion of timing components.
"""

import json
import os
import glob
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from pathlib import Path
import seaborn as sns

# Set up matplotlib for high-quality academic plots
import matplotlib

matplotlib.rcParams["font.family"] = "Arial"
matplotlib.rcParams["pdf.fonttype"] = 42
matplotlib.rcParams["ps.fonttype"] = 42
matplotlib.rcParams["font.size"] = 12
matplotlib.rcParams["axes.titlesize"] = 12
matplotlib.rcParams["axes.labelsize"] = 9
matplotlib.rcParams["xtick.labelsize"] = 8
matplotlib.rcParams["ytick.labelsize"] = 9
matplotlib.rcParams["legend.fontsize"] = 8


# Additional settings for high-quality plots
plt.rcParams.update(
    {
        "axes.linewidth": 1.2,
        "axes.spines.top": True,
        "axes.spines.right": True,
        "xtick.major.size": 6,
        "xtick.minor.size": 3,
        "ytick.major.size": 6,
        "ytick.minor.size": 3,
        "legend.frameon": True,
        "legend.fancybox": False,
        "legend.edgecolor": "black",
        "legend.framealpha": 1.0,
        "figure.dpi": 300,
        "savefig.dpi": 300,
        "savefig.bbox": "tight",
        "savefig.pad_inches": 0.1,
    }
)


def load_summary_statistics(base_path):
    """
    Load summary statistics from all QPS directories.

    Args:
        base_path (str): Base path to the evaluation results

    Returns:
        dict: Dictionary with QPS values as keys and metrics as values
    """
    data = {}

    # Pattern to match QPS directories
    qps_pattern = os.path.join(base_path, "*", "hybrid", "logs", "summary_statistics.json")

    for file_path in glob.glob(qps_pattern):
        # Extract QPS value from directory name
        qps_dir = Path(file_path).parent.parent.parent.name
        try:
            qps_value = float(qps_dir)
        except ValueError:
            print(f"Warning: Could not parse QPS value from directory name: {qps_dir}")
            continue

        # Load JSON data
        try:
            with open(file_path, 'r') as f:
                json_data = json.load(f)

            # Extract average metrics
            metrics = json_data.get('average_metrics', {})

            # Ensure all required metrics are present
            required_metrics = [
                "prefill_queue_time",
                "prefill_scheduling_time",
                "prefill_execution_time",
                "transfer_time",
                "decoding_scheduling_time",
                "decoding_execution_time",
            ]

            if all(metric in metrics for metric in required_metrics):
                data[qps_value] = metrics
            else:
                print(f"Warning: Missing metrics in {file_path}")

        except (json.JSONDecodeError, FileNotFoundError) as e:
            print(f"Error loading {file_path}: {e}")

    return data


def calculate_percentages(data):
    """
    Calculate percentage breakdown for each QPS value.

    Args:
        data (dict): Raw timing data

    Returns:
        tuple: (qps_values, percentage_data)
    """
    qps_values = sorted(data.keys())

    # Initialize arrays for each timing component
    prefill_queue_pct = []
    prefill_scheduling_pct = []
    prefill_execution_pct = []
    transfer_pct = []
    decoding_scheduling_pct = []
    decoding_execution_pct = []

    for qps in qps_values:
        metrics = data[qps]

        # Calculate total time
        total_time = (
            metrics["prefill_queue_time"]
            + metrics["prefill_scheduling_time"]
            + metrics["prefill_execution_time"]
            + metrics["transfer_time"]
            + metrics["decoding_scheduling_time"]
            + metrics["decoding_execution_time"]
        )

        # Calculate percentages
        prefill_queue_pct.append((metrics["prefill_queue_time"] / total_time) * 100)
        prefill_scheduling_pct.append(
            (metrics["prefill_scheduling_time"] / total_time) * 100
        )
        prefill_execution_pct.append(
            (metrics["prefill_execution_time"] / total_time) * 100
        )
        transfer_pct.append((metrics["transfer_time"] / total_time) * 100)
        decoding_scheduling_pct.append(
            (metrics["decoding_scheduling_time"] / total_time) * 100
        )
        decoding_execution_pct.append(
            (metrics["decoding_execution_time"] / total_time) * 100
        )

    percentage_data = {
        "prefill_queue_time": np.array(prefill_queue_pct),
        "prefill_scheduling_time": np.array(prefill_scheduling_pct),
        "prefill_execution_time": np.array(prefill_execution_pct),
        "transfer_time": np.array(transfer_pct),
        "decoding_scheduling_time": np.array(decoding_scheduling_pct),
        "decoding_execution_time": np.array(decoding_execution_pct),
    }

    return qps_values, percentage_data


def create_stacked_plot(qps_values, percentage_data, output_path=None):
    """
    Create a stacked bar chart following the reference code style.

    Args:
        qps_values (list): List of QPS values
        percentage_data (dict): Dictionary of percentage arrays
        output_path (str): Output file path
    """
    # Set default output path to .cache directory in current file's directory
    if output_path is None:
        current_file_dir = Path(__file__).parent
        cache_dir = current_file_dir / ".cache"
        cache_dir.mkdir(exist_ok=True)  # Create .cache directory if it doesn't exist
        output_path = cache_dir / "latency_breakdown_analysis.pdf"

    # Define colors for all components including new scheduling metrics
    colors = ["orange", "green", "#AAF08C", "red", "blue", "#98C7F6"]

    # Create figure and axis
    fig, ax = plt.subplots(figsize=(2.8, 2))

    # Bar width as in reference code
    bar_width = 0.5

    # Component labels for legend (matching reference style)
    labels = [
        "P Sched",
        "P Queue",
        "P Exec",
        "Transfer",
        "D Sched",
        "D Exec",
    ]

    # Component order (bottom to top) - positioning scheduling metrics as requested
    component_order = [
        "prefill_scheduling_time",
        "prefill_queue_time",
        "prefill_execution_time",
        "transfer_time",
        "decoding_scheduling_time",
        "decoding_execution_time",
    ]

    # Initialize bottoms array as in reference code
    bottoms = np.zeros(len(qps_values))

    # Plot each component following reference code pattern
    for index, component in enumerate(component_order):
        ax.bar(
            [f"{qps}" for qps in qps_values],  # X-axis labels as strings
            percentage_data[component],
            bar_width,
            label=labels[index],
            bottom=bottoms,
            color=colors[index],
        )
        bottoms += percentage_data[component]

    # Set axis labels exactly as in reference code
    ax.set_xlabel("Request rate (req/s)")
    ax.set_ylabel("Latency Breakdown (%)")

    # Set legend with 3 columns to accommodate 6 metrics
    ax.legend(frameon=False, loc=(-0.12, 1.0), ncol=3, columnspacing=0.5)

    # Set y-axis to show 0-100%
    ax.set_ylim(0, 100)

    # Save the plot
    plt.tight_layout()
    plt.savefig(
        output_path,
        dpi=300,
        bbox_inches="tight",
        facecolor="white",
        edgecolor="none",
    )
    plt.show()

    print(f"Plot saved to: {output_path}")


def print_summary_table(qps_values, percentage_data):
    """
    Print a summary table of the percentage breakdown.

    Args:
        qps_values (list): List of QPS values
        percentage_data (dict): Dictionary of percentage arrays
    """
    print("\n" + "=" * 100)
    print("LATENCY BREAKDOWN SUMMARY TABLE")
    print("=" * 100)
    print(
        f"{'QPS':<8} {'P Sched':<10} {'P Queue':<10} {'P Exec':<10} {'Transfer':<10} {'D Sched':<10} {'D Exec':<10}"
    )
    print("-" * 100)

    for i, qps in enumerate(qps_values):
        print(
            f"{qps:<8.2f} "
            f"{percentage_data['prefill_scheduling_time'][i]:<10.3f} "
            f"{percentage_data['prefill_queue_time'][i]:<10.3f} "
            f"{percentage_data['prefill_execution_time'][i]:<10.3f} "
            f"{percentage_data['transfer_time'][i]:<10.3f} "
            f"{percentage_data['decoding_scheduling_time'][i]:<10.3f} "
            f"{percentage_data['decoding_execution_time'][i]:<10.3f}"
        )

    print("=" * 100)
    print("All values are percentages of total time per QPS level")


def main():
    """Main function to execute the analysis."""
    # Define the base path to evaluation results (relative to current file)
    current_file_dir = Path(__file__).parent
    base_path = (
        current_file_dir.parent
        / "latency_breakdown"
        / ".cache"
        / "Qwen"
        / "Qwen2.5-14B"
        / "arxiv"
    )

    print("Loading latency breakdown data...")

    # Load data from all QPS directories
    raw_data = load_summary_statistics(base_path)

    if not raw_data:
        print("Error: No valid data found. Please check the file paths.")
        return

    print(f"Found data for {len(raw_data)} QPS levels: {sorted(raw_data.keys())}")

    # Calculate percentages
    qps_values, percentage_data = calculate_percentages(raw_data)

    # Create both plots
    print("Creating academic-style stacked bar plot...")
    create_stacked_plot(qps_values, percentage_data)

    # Print summary table
    print_summary_table(qps_values, percentage_data)

    print("\nAnalysis complete!")


if __name__ == "__main__":
    main()
