#!/usr/bin/env python3
"""
Raw log data collection and processing script for concurrent prefill analysis.

This script:
1. Extracts end-to-end results from benchmark.log files
2. Collects batch information from instance_*.log files
3. Analyzes concurrent prefill token loads for decode phase requests
4. Maps performance metrics to concurrent prefill loads
5. Saves processed data for further analysis
"""

import argparse
import json
import os
import re
import sys
import glob
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any


def extract_benchmark_report(file_path: str) -> Optional[Dict[str, Any]]:
    """
    Extract end-to-end results from benchmark.log file.
    
    Args:
        file_path (str): Path to benchmark.log file
        
    Returns:
        Dict containing performance metrics or None if not found
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Match "Full report_data: " followed by JSON string
        pattern = r'Full report_data:\s*(.+?)(?=\n|$)'
        matches = re.findall(pattern, content, re.MULTILINE)
        
        if len(matches) != 1:
            print(f"Warning: Expected 1 'Full report_data' match, got {len(matches)} in {file_path}")
            return None
            
        try:
            json_data = json.loads(matches[0].strip())
            print(f"✓ Extracted benchmark report from {file_path}")
            return json_data
        except json.JSONDecodeError as e:
            print(f"Error: Failed to parse JSON from {file_path}: {e}")
            return None
            
    except FileNotFoundError:
        print(f"Error: Benchmark file not found: {file_path}")
        return None
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None


def extract_batch_info_from_file(file_path: str) -> List[Dict[str, Any]]:
    """
    Extract batch information from a single instance log file.
    
    Args:
        file_path (str): Path to instance_*.log file
        
    Returns:
        List of batch_info dictionaries
    """
    batch_infos = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if '[Scheduler] Batch Info:' in line:
                    try:
                        # Extract JSON part after "[Scheduler] Batch Info: "
                        json_start = line.find('{')
                        if json_start == -1:
                            continue
                            
                        json_str = line[json_start:]
                        batch_info = json.loads(json_str)
                        
                        # Validate required fields
                        if ('scheduled_seq_group_ids' in batch_info and 
                            'scheduled_tokens' in batch_info):
                            batch_infos.append(batch_info)
                        else:
                            print(f"Warning: Missing required fields in batch info at {file_path}:{line_num}")
                            
                    except json.JSONDecodeError as e:
                        print(f"Warning: Failed to parse batch info JSON at {file_path}:{line_num}: {e}")
                        continue
                        
    except FileNotFoundError:
        print(f"Warning: Instance log file not found: {file_path}")
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return batch_infos


def collect_all_batch_info(directory: str) -> List[Dict[str, Any]]:
    """
    Scan all instance_*.log files and collect batch information.
    
    Args:
        directory (str): Directory containing instance log files
        
    Returns:
        List of all batch_info dictionaries from all files
    """
    all_batch_infos = []
    
    # Find all instance_*.log files
    pattern = os.path.join(directory, "instance_*.log")
    instance_files = glob.glob(pattern)
    
    if not instance_files:
        print(f"Warning: No instance_*.log files found in {directory}")
        return all_batch_infos
        
    print(f"Found {len(instance_files)} instance log files")
    
    for file_path in sorted(instance_files):
        batch_infos = extract_batch_info_from_file(file_path)
        all_batch_infos.extend(batch_infos)
        print(f"✓ Extracted {len(batch_infos)} batch infos from {os.path.basename(file_path)}")
        
    print(f"Total batch infos collected: {len(all_batch_infos)}")
    return all_batch_infos


def convert_request_id(request_id: str) -> Optional[int]:
    """
    Convert request ID from format 'ID-XXXX-X' to numeric ID.
    
    Args:
        request_id (str): Request ID in format 'ID-XXXX-X'
        
    Returns:
        Numeric ID or None if conversion fails
    """
    if not isinstance(request_id, str) or not request_id.startswith('ID-'):
        return None
        
    try:
        # Extract numeric part after 'ID-' and before '-X'
        numeric_part = request_id[3:].split('-')[0]
        return int(numeric_part)
    except ValueError:
        return None


def analyze_concurrent_prefill(batch_infos: List[Dict[str, Any]]) -> Dict[int, int]:
    """
    Analyze concurrent prefill token loads for decode phase requests.
    
    Args:
        batch_infos: List of batch information dictionaries
        
    Returns:
        Dictionary mapping request_id to concurrent prefill token counts
    """
    concurrent_prefill_data: dict[int, int] = {}
    
    print(f"Analyzing {len(batch_infos)} batch infos...")
    print(f"batch 0 {batch_infos[0]}")
    
    for batch_info in batch_infos:
        seq_group_ids = batch_info.get('scheduled_seq_group_ids', [])
        scheduled_tokens = batch_info.get('scheduled_tokens', [])
        
        assert len(seq_group_ids) == len(scheduled_tokens), (
            f"Length mismatch: {len(seq_group_ids)} vs {len(scheduled_tokens)}")
            
        # Convert request IDs and filter valid ones
        numeric_ids = []
        valid_tokens = []
        
        for req_id, tokens in zip(seq_group_ids, scheduled_tokens):
            numeric_id = convert_request_id(req_id)
            if numeric_id is not None:
                numeric_ids.append(numeric_id)
                valid_tokens.append(tokens)
        
        concurrent_prefill_tokens = sum(
            n_token for n_token in valid_tokens if n_token > 1
        )
        
        # Calculate concurrent prefill tokens for decode requests
        for i, (req_id, tokens) in enumerate(zip(numeric_ids, valid_tokens)):
            if tokens == 1:  # Decode phase request
                 concurrent_prefill_data[req_id] = concurrent_prefill_data.get(req_id, 0) + concurrent_prefill_tokens
    
    print(f"✓ Analyzed concurrent prefill for {len(concurrent_prefill_data)} requests")
    return concurrent_prefill_data


def process_and_save_data(benchmark_data: Dict[str, Any], 
                         concurrent_prefill_data: Dict[int, int], 
                         output_dir: str) -> str:
    """
    Process data and save to cache directory.
    
    Args:
        benchmark_data: Performance metrics from benchmark report
        concurrent_prefill_data: Concurrent prefill analysis results
        output_dir: Output directory path
        
    Returns:
        Path to saved file
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Extract tpots data
    tpots = benchmark_data.get('tpots', [])
    if not tpots:
        raise ValueError("No 'tpots' data found in benchmark report")

    output_lengths = benchmark_data.get('output_lengths', [])
    if not output_lengths:
        raise ValueError("No 'output_lengths' data found in benchmark report")

    # Discard first 8 requests (IDs 0-7) and renumber
    processed_data = []

    for original_id, prefill_loads in concurrent_prefill_data.items():
        if original_id < 8:  # Skip first 8 requests
            continue

        new_id = original_id - 8  # Renumber starting from 0

        if new_id >= len(tpots):
            print(f"Warning: Request ID {new_id} exceeds tpots data length ({len(tpots)})")
            continue

        processed_data.append({
            'request_id': new_id,
            'tpot': tpots[new_id],
            'output_length': output_lengths[new_id],
            'concurrent_prefill_tokens': prefill_loads,
            'interference_intensity': prefill_loads / output_lengths[new_id]
        })

    # Sort by request ID
    processed_data.sort(key=lambda x: x['request_id'])

    # Save processed data
    output_file = os.path.join(output_dir, 'concurrent_prefill_analysis.json')

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(processed_data, f, indent=2, ensure_ascii=False)

    print(f"✓ Processed data saved to: {output_file}")
    print(f"  - Analyzed {len(processed_data)} requests")
    print(f"  - Discarded first 8 requests (warmup requests) as specified")

    return output_file


def validate_input_directory(directory: str) -> bool:
    """Validate that the input directory contains required files."""
    if not os.path.isdir(directory):
        print(f"❌ Directory does not exist: {directory}")
        return False

    benchmark_file = os.path.join(directory, 'benchmark.log')
    if not os.path.isfile(benchmark_file):
        print(f"❌ benchmark.log not found in {directory}")
        return False

    instance_files = glob.glob(os.path.join(directory, "instance_*.log"))
    if not instance_files:
        print(f"❌ No instance_*.log files found in {directory}")
        return False

    print(f"✓ Found benchmark.log and {len(instance_files)} instance log files")
    return True


def main():
    parser = argparse.ArgumentParser(
        description='Collect and process raw log data for concurrent prefill analysis',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s /path/to/logs
  %(prog)s /path/to/logs -o custom_analysis.json

The script expects:
  - benchmark.log: Contains "Full report_data: <json>" line
  - instance_*.log: Contains "[Scheduler] Batch Info: <json>" lines
        """
    )

    parser.add_argument(
        'directory',
        help='Directory path containing benchmark.log and instance_*.log files'
    )

    parser.add_argument(
        '-o', '--output',
        default='concurrent_prefill_analysis.json',
        help='Output filename (default: concurrent_prefill_analysis.json)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )

    args = parser.parse_args()

    # Validate input directory
    if not validate_input_directory(args.directory):
        sys.exit(1)

    # Get script directory for cache location
    script_dir = os.path.dirname(os.path.abspath(__file__))
    cache_dir = os.path.join(script_dir, '.cache', 'data')

    print(f"📁 Processing logs from directory: {args.directory}")
    print(f"💾 Output will be saved to: {cache_dir}")
    print()

    # Step 1: Extract benchmark report
    benchmark_file = os.path.join(args.directory, 'benchmark.log')
    benchmark_data = extract_benchmark_report(benchmark_file)

    if not benchmark_data:
        print("❌ Failed to extract benchmark data")
        sys.exit(1)

    # Step 2: Collect batch information
    print("\n📊 Collecting batch information from instance logs...")
    batch_infos = collect_all_batch_info(args.directory)

    if not batch_infos:
        print("❌ No batch information collected")
        sys.exit(1)

    # Step 3: Analyze concurrent prefill loads
    print("\n🔍 Analyzing concurrent prefill token loads...")
    concurrent_prefill_data = analyze_concurrent_prefill(batch_infos)

    if not concurrent_prefill_data:
        print("❌ No concurrent prefill data generated")
        sys.exit(1)

    print(f"✓ Analyzed concurrent prefill for {len(concurrent_prefill_data)} requests")

    # Step 4: Process and save data
    print("\n💾 Processing and saving data...")
    try:
        # Modify process_and_save_data to return both file path and processed data
        output_file = process_and_save_data(benchmark_data, concurrent_prefill_data, cache_dir)

        # Load the saved data for statistics
        with open(output_file, 'r') as f:
            saved_data = json.load(f)

        if args.verbose:
            print_summary_statistics(concurrent_prefill_data, saved_data['data'])

        print(f"\n🎉 Processing completed successfully!")
        print(f"📄 Results saved to: {output_file}")

    except Exception as e:
        print(f"❌ Error during data processing: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
