#!/usr/bin/env python3
"""
Plotting function for concurrent prefill analysis following the established patterns.

This script creates a visualization showing the relationship between concurrent prefill 
token loads and request performance (tpot values) using the processed data from 
collect_raw_log_data.py.
"""

import os
import json
import argparse
import math
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import AutoMinorLocator
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional

# Import plot utilities
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from plot_utils import crop_margins

# Set up matplotlib for academic-style plots following the established pattern
import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 8
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 9
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10

# 数据接口
def get_data(file_path: str) -> tuple[list[float], list[float]]:
    """
    从JSON文件中获取数据
    Args:
        file_path: JSON文件路径
    Returns:
        tuple: (TPOT列表, 干扰强度列表)
    """
    with open(file_path, 'r') as f:
        data = json.load(f)

    # 检查是否是一个列表
    if not isinstance(data, list):
        raise ValueError(f"Expected list, got {type(data)}")
    
    # 从每个列表元素中取出 tpot 和 interference_intensity
    tpots = [item['tpot'] for item in data]
    interference_intensities = [item['interference_intensity'] for item in data]
    
    return tpots, interference_intensities

    

class ConcurrentPrefillPlotter:
    """Plotter for concurrent prefill analysis following established patterns."""

    def __init__(self, data_file: str, output_dir: str = None):
        """
        Initialize the plotter.

        Args:
            data_file: Path to the concurrent_prefill_analysis.json file
            output_dir: Output directory for plots (default: .cache directory)
        """
        self.data_file = data_file
        self.script_dir = os.path.dirname(os.path.abspath(__file__))

        if output_dir is None:
            self.output_dir = os.path.join(self.script_dir, '.cache')
        else:
            self.output_dir = output_dir

        os.makedirs(self.output_dir, exist_ok=True)

        # Load data using the get_data function
        self.tpots, self.interference_intensities = get_data(self.data_file)

    def _extract_plot_data(self) -> Tuple[np.ndarray, np.ndarray, str]:
        """Extract x and y data for plotting using the loaded data."""
        x_data = np.array(self.interference_intensities)
        # Convert TPOT from seconds to milliseconds
        y_data = np.array(self.tpots) * 1000
        x_label = "Interference Intensity"

        return x_data, y_data, x_label
    
    def plot_scatter_with_fit(self, plot_name: str = "concurrent_prefill_tpot",
                             figsize: Tuple[float, float] = (2.8, 2),
                             intercept_value: float = 100.0) -> str:
        """
        Create scatter plot with fitted line following the example.py pattern.

        Args:
            plot_name: Name for the output file
            figsize: Figure size tuple
            intercept_value: Y-value for intercept line in ms

        Returns:
            Path to saved plot file
        """
        fig, ax = plt.subplots(figsize=figsize)
        plt.rcParams.update({"font.size": 12})
        
        # Extract data
        x_data, y_data, x_label = self._extract_plot_data()
        
        if len(x_data) == 0:
            raise ValueError("No data available for plotting")
        
        # Create scatter plot
        plt.scatter(
            x_data,
            y_data,
            color='lightgray',
            alpha=0.7,
            s=20
        )
        
        # Fit linear regression
        coefficients = np.polyfit(x_data, y_data, 1)
        polynomial = np.poly1d(coefficients)
        fitted_values = polynomial(x_data)
        
        # Plot fitted line
        plt.plot(x_data, fitted_values, label="Fitted line", color="tab:blue", linewidth=2)
        
        # Calculate R-squared
        residuals = y_data - fitted_values
        ss_res = np.sum(residuals**2)
        ss_tot = np.sum((y_data - np.mean(y_data)) ** 2)
        r_squared = 1 - (ss_res / ss_tot)
        
        # Add formula and R-squared text (without background box)
        formula = f"y = {coefficients[0]:.6f}x + {coefficients[1]:.6f}"
        plt.text(
            0.04,
            0.68,
            f"{formula}\n$R^2$ = {r_squared:.3f}",
            transform=plt.gca().transAxes,
            fontsize=9,
            verticalalignment="bottom",
            horizontalalignment="left"
        )

        # 画出截距线和拟合线的交点，并且在交点处向x轴画一条线(都是用虚线)
        y_intercept = intercept_value  # 使用参数化的截距值
        x_intercept = (y_intercept - coefficients[1]) / coefficients[0]
        plt.axhline(
            y=y_intercept,
            color="black",
            linestyle=":",
            xmax=x_intercept / max(x_data),
        )
        plt.axvline(
            x=x_intercept,
            color="black",
            linestyle=":",
            ymax=y_intercept / max(y_data) / 1.1,
        )
        plt.annotate(
            f"{x_intercept:.2f}",
            xy=(x_intercept, 0),
            xytext=(x_intercept - max(x_data) * 0.2, max(y_data) * 0.15),
            arrowprops=dict(facecolor="black", arrowstyle="->", color="black"),
            fontsize=8,
            color="black",
            horizontalalignment="left",
        )

        # Set axis limits
        x_margin = (max(x_data) - min(x_data)) * 0.05
        x_max_floor_hundred = math.floor(max(x_data) / 100) * 100
        plt.xlim(0, x_max_floor_hundred)

        # Set Y-axis from 0 to ceiling of max value rounded to nearest hundred
        y_max_ceil_hundred = math.ceil(max(y_data) / 100) * 100
        plt.ylim(0, y_max_ceil_hundred)
        
        # Add minor ticks
        ax.yaxis.set_minor_locator(AutoMinorLocator(5))
        ax.xaxis.set_minor_locator(AutoMinorLocator(5))
        
        # Add grid
        plt.grid(axis='x', color='lightgray', linestyle='--', alpha=0.7)
        plt.grid(axis='y', color='lightgray', linestyle='--', alpha=0.7)
        
        # Labels and legend
        plt.xlabel(x_label)
        plt.ylabel("TPOT (ms)")
        plt.legend(loc="lower right", frameon=False)
        
        # Ensure all spines are visible (top and right borders)
        for spine in ax.spines.values():
            spine.set_visible(True)
            spine.set_color('black')
            spine.set_linewidth(0.5)
        
        plt.tight_layout()
        
        # Save plot
        save_path = os.path.join(self.output_dir, f"{plot_name}.pdf")
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Figure saved to {save_path}")
        
        # Crop margins
        try:
            cropped_path = crop_margins(save_path)
            print(f"Cropped to {cropped_path} successfully.")
            return str(cropped_path)
        except Exception as e:
            print(f"Warning: Could not crop margins: {e}")
            return save_path
    



def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(
        description='Create concurrent prefill analysis plots',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s
  %(prog)s --data-file custom_analysis.json
  %(prog)s --output-dir ./plots --plot-name custom_plot
        """
    )
    
    parser.add_argument(
        '--data-file',
        default='.cache/data/concurrent_prefill_analysis.json',
        help='Path to the analysis data file (default: .cache/data/concurrent_prefill_analysis.json)'
    )
    
    parser.add_argument(
        '--output-dir',
        help='Output directory for plots (default: .cache)'
    )
    
    parser.add_argument(
        '--plot-name',
        default='concurrent_prefill_tpot',
        help='Name for the output plot file (default: concurrent_prefill_tpot)'
    )
    

    
    parser.add_argument(
        '--figsize',
        nargs=2,
        type=float,
        default=[2.8, 2.0],
        help='Figure size as width height (default: 2.8 2.0)'
    )

    parser.add_argument(
        '--intercept-value',
        type=float,
        default=100.0,
        help='Y-value for intercept line in ms (default: 100.0)'
    )
    

    
    args = parser.parse_args()
    
    # Resolve data file path
    if not os.path.isabs(args.data_file):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        data_file = os.path.join(script_dir, args.data_file)
    else:
        data_file = args.data_file
    
    try:
        # Validate data file exists
        if not os.path.exists(data_file):
            print(f"❌ Error: Data file not found: {data_file}")
            return 1

        # Create plotter
        plotter = ConcurrentPrefillPlotter(data_file, args.output_dir)

        # Create plot
        output_path = plotter.plot_scatter_with_fit(
            plot_name=args.plot_name,
            figsize=tuple(args.figsize),
            intercept_value=args.intercept_value
        )

        print(f"\n🎉 Plot created successfully!")
        print(f"📄 Output saved to: {output_path}")

    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
        return 1
    except ValueError as e:
        print(f"❌ Data format error: {e}")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
