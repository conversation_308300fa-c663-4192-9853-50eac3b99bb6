import os
import dataclasses
import matplotlib.pyplot as plt

from pathlib import Path
from plot_utils import crop_margins
from typing import Tuple
from plot_utils import PD_COLOR, CP_COLOR, HYBRID_COLOR, SYS_NAME


# 原始数据
# data =  workload_name -> (qps_list, results)
# 其中 results = list of (scheduler_name, attained_rate_list)
data: dict[str, tuple[list[float], list[tuple[str, list[float]]]]] = {
    "14b_sharegpt": ([24.0, 26.0, 28.0, 30.0, 32.0, 34.0, 36.0], [('hybrid', [99.70703125, 99.70703125, 99.31640625, 99.609375, 99.31640625, 93.65234375, 85.9375]), ('pd_disaggregation', [100.0, 55.17578125, 40.234375, 26.26953125, 21.97265625, 20.21484375, 19.23828125]), ('chunked_prefill', [99.609375, 99.0234375, 97.4609375, 94.43359375, 87.98828125, 83.0078125, 75.48828125])]),    "32b_sharegpt": [('hybrid', [100.0, 99.70703125, 99.21875, 97.16796875, 94.62890625, 87.98828125]), ('pd_disaggregation', [99.90234375, 49.12109375, 24.8046875, 19.7265625, 18.9453125, 15.8203125]), ('chunked_prefill', [99.51171875, 98.92578125, 97.94921875, 94.62890625, 85.546875, 71.875])],
    "32b_sharegpt": ([18.0, 20.0, 22.0, 24.0, 26.0, 28.0], [('hybrid', [100.0, 99.70703125, 99.21875, 97.16796875, 94.62890625, 87.98828125]), ('pd_disaggregation', [99.90234375, 49.12109375, 24.8046875, 19.7265625, 18.9453125, 15.8203125]), ('chunked_prefill', [99.51171875, 98.92578125, 97.94921875, 94.62890625, 85.546875, 71.875])]),
    "14b_sharegpt_tpot": ([22.0, 24.0, 26.0, 28.0, 30.0, 32.0, 34.0, 36.0], [('hybrid', [99.51171875, 99.0234375, 98.14453125, 95.3125, 96.19140625, 89.2578125, 79.00390625, 66.2109375]), ('pd_disaggregation', [100.0, 100.0, 65.8203125, 49.21875, 40.91796875, 30.37109375, 25.1953125, 23.14453125]), ('chunked_prefill', [97.8515625, 96.58203125, 87.890625, 72.0703125, 60.9375, 55.6640625, 44.82421875, 37.01171875])]),
    "32b_sharegpt_tpot": ([18.0, 20.0, 22.0, 24.0, 26.0, 28.0], [('hybrid', [98.92578125, 99.70703125, 97.55859375, 94.921875, 88.0859375, 72.75390625]), ('pd_disaggregation', [100.0, 64.453125, 36.71875, 24.8046875, 21.09375, 19.23828125]), ('chunked_prefill', [97.36328125, 92.67578125, 83.0078125, 61.71875, 49.21875, 37.890625])]),
    "14b_arxiv": ([1.25, 1.5, 1.75, 2.0, 2.25, 2.5], [('hybrid', [99.21875, 99.0234375, 99.0234375, 97.65625, 92.578125, 56.25]), ('pd_disaggregation', [91.9921875, 74.21875, 41.9921875, 8.7890625, 7.6171875, 6.8359375]), ('chunked_prefill', [99.609375, 99.21875, 91.6015625, 79.8828125, 59.765625, 31.8359375])]),
    "32b_arxiv": ([1.0, 1.25, 1.5, 1.75, 2.0], [('hybrid', [97.8515625, 97.4609375, 95.703125, 89.6484375, 77.734375]), ('pd_disaggregation', [87.6953125, 64.6484375, 28.90625, 7.421875, 5.6640625]), ('chunked_prefill', [98.046875, 92.1875, 76.3671875, 54.296875, 34.375])]),    "14b_arxiv_tpot": [('hybrid', [100.0, 100.0, 99.8046875, 98.2421875, 95.3125, 85.546875]), ('pd_disaggregation', [99.4140625, 98.828125, 93.75, 66.015625, 11.9140625, 9.765625]), ('chunked_prefill', [98.828125, 96.484375, 88.4765625, 71.875, 51.5625, 32.6171875])],
    "14b_arxiv_tpot": ([1.0, 1.25, 1.5, 1.75, 2.0, 2.25], [('hybrid', [100.0, 100.0, 99.8046875, 98.2421875, 95.3125, 85.546875]), ('pd_disaggregation', [99.4140625, 98.828125, 93.75, 66.015625, 11.9140625, 9.765625]), ('chunked_prefill', [98.828125, 96.484375, 88.4765625, 71.875, 51.5625, 32.6171875])]),
    "32b_arxiv_tpot": ([1.0, 1.25, 1.5, 1.75, 2.0], [('hybrid', [97.65625, 95.703125, 91.015625, 83.203125, 59.765625]), ('pd_disaggregation', [97.8515625, 86.71875, 47.0703125, 9.375, 7.8125]), ('chunked_prefill', [96.09375, 89.0625, 69.140625, 47.4609375, 29.6875])]),    
}

@dataclasses.dataclass
class Backend:
    name: str
    label: str
    color: str
    marker: str = "o"
    zorder: int = 0

def find_intersection(
    xs: list[float], ys: list[float], target_y: float
) -> Tuple[float, float]:
    """
    Find the intersection point of the given curve (indicated by xs and ys), and
    return the X axis and Y axis of the point.
    """
    for index in range(len(xs) - 1):
        x0 = xs[index]
        x1 = xs[index + 1]
        y0 = ys[index]
        y1 = ys[index + 1]
        if (y0 < target_y) != (y1 < target_y):
            # Intersection point found!
            inter_x = (target_y - y0) * (x1 - x0) / (y1 - y0) + x0
            return (inter_x, target_y)
    print(
        f"WARNING: Intersection point not found! xs: {xs}, ys: {ys}, target_y: {target_y}"
    )
    return (xs[0], target_y)


def plot_goodput(
    qps_list: list[float],
    results: list[tuple[str, list[float]]],
    methods: list[str],
    save_path: str = "goodput.pdf",
    is_crop_margins: bool = False,
    with_legend: bool = True,
):
    backend_styles = {
        "hybrid": Backend("hybrid", SYS_NAME, HYBRID_COLOR, "o", 3),
        "pd_disaggregation": Backend("pd", "PD-Disaggregation", PD_COLOR, "v", 2),
        "chunked_prefill": Backend("cp", "PD-Aggregation", CP_COLOR, "D", 1),
    }
    attainment_target = 90
    plt.rcParams.update({"font.size": 11})
    
    if with_legend:
        fig, ax = plt.subplots(figsize=(6, 4))
    else:
        fig, ax = plt.subplots(figsize=(3, 2))
    attained_rates_for_methods = {
        scheduler: rates
        for scheduler, rates in results
    }
    for scheduler in methods:
        attained_rates = attained_rates_for_methods[scheduler]
        style = backend_styles[scheduler]
        ax.plot(
            qps_list,
            attained_rates,
            label=style.label,
            color=style.color,
            marker=style.marker,
            zorder=style.zorder,
        )
        intersection_x, intersection_y = find_intersection(
            list(map(float, qps_list)),
            attained_rates,
            attainment_target,
        )
        ax.vlines(
            x=intersection_x,
            ymin=0,
            ymax=intersection_y,
            linestyles="--",
            colors=style.color,
        )
        ax.axhline(y=attainment_target, color="grey", linestyle="--")

    ax.set_xlabel("Rate (reqs/s)")
    ax.set_ylabel("SLO Attainment (%)")
    ax.set_xlim(min(qps_list) * 0.95, max(qps_list) * 1.05)

    if with_legend:
        fig.legend(ncol=3, loc="outside upper center", frameon=False)
        fig.tight_layout()
        fig.subplots_adjust(top=0.60)
    else:
        fig.tight_layout()

    os.makedirs(Path(save_path).parent, exist_ok=True)
    fig.savefig(save_path)
    print(f"Saved plot to {save_path}")
    if is_crop_margins:
        crop_margins(save_path)


def plot_goodput_legend(save_path: str):
    plot_goodput(
        qps_list,
        results,
        methods,
        save_path,
        is_crop_margins=True,
    )
    crop_margins(save_path, "-p4 10 -2100 10 10")


if __name__ == "__main__":
    methods = ["chunked_prefill", "pd_disaggregation", "hybrid"]
    for name, (qps_list, results) in data.items():
        plot_goodput(
            qps_list,
            results,
            methods,
            save_path=f".cache/{name}_slo_attainment.pdf",
            is_crop_margins=True,
            with_legend=False,
        )
    plot_goodput_legend(".cache/slo_attainment_legend.pdf")
