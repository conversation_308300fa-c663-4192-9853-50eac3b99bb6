import pdfCropMargins

from matplotlib.figure import Figure
from pathlib import Path

SYS_NAME = "TaiChi"

CP_COLOR = "tab:green"
PD_COLOR = "tab:orange"
HYBRID_COLOR = "tab:blue"
GRADIENT_REDS = [
    ("tab:orange", 0.6),
    ("tab:orange", 0.8),
    ("tab:red", 1),
]

def crop_margins(image_path: str, crop_args: str = "-p 10"):
    crop_path = Path(image_path)
    crop_path = crop_path.with_stem(crop_path.stem + "_cropped")
    pdfCropMargins.crop(crop_args.split() + f"{image_path} -o {crop_path}".split())
    return crop_path


def get_deduplicate_legends(fig: Figure):
    handles, labels = fig.gca().get_legend_handles_labels()
    legends = dict(zip(labels, handles))
    handles, labels = list(legends.values()), list(legends.keys())
    return handles, labels
