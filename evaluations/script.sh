#! /bin/bash
set -x



bash /root/PD-vllm/evaluations/new_32b_sharegpt_tpot/.cache/Qwen/Qwen2.5-32B/sharegpt/scripts/evaluate_all_qps.sh

bash /root/PD-vllm/evaluations/new_32b_arxiv_tpot/.cache/Qwen/Qwen2.5-32B/arxiv/scripts/evaluate_all_qps.sh

bash /root/PD-vllm/evaluations/new_14b_sharegpt_tpot/.cache/Qwen/Qwen2.5-14B/sharegpt/scripts/evaluate_all_qps.sh

bash /root/PD-vllm/evaluations/new_14b_arxiv_tpot/.cache/Qwen/Qwen2.5-14B/arxiv/scripts/evaluate_all_qps.sh



# bash /root/PD-vllm/evaluations/ablation_base/.cache/Qwen/Qwen2.5-14B/arxiv/scripts/evaluate_all_qps.sh

# bash /root/PD-vllm/evaluations/ablation_base_arch/.cache/Qwen/Qwen2.5-14B/arxiv/scripts/evaluate_all_qps.sh

# bash /root/PD-vllm/evaluations/ablation_base_arch_decoding/.cache/Qwen/Qwen2.5-14B/arxiv/scripts/evaluate_all_qps.sh

# bash /root/PD-vllm/evaluations/ablation_base_arch_decoding_prefill/.cache/Qwen/Qwen2.5-14B/arxiv/scripts/evaluate_all_qps.sh