### 配置数据集负载 ###
selected_dataset: 2

qps_list: [18, 20, 22, 24, 26, 28] 
num_prompts: 1024

### 配置指标 ###
steady_state_start: 0
steady_state_end: 1
ttft_slo: 3
tpot_slo: 0.120

slo_scale_qps: 26
slo_scale_list: [7, 6, 5, 4, 3, 2, 1, 0.95]

### 配置模型 ###
selected_model: 2
world_size: 4
tp_size: 2

watermark: 0.05
load_aware_buffer_length: 8

slo_dict:
  APIKEY_None: [3, 1]
  APIKEY_Summarization: [4, 0.070]
  APIKEY_Chatbot: [3, 0.120]


### 配置测试对象 ###
schedulers:
  hybrid:
    decoding_size: 2
    # enable_flowing: "" # store_true 类型的参数
    enable_flowing: "--enable_flowing" # store_true 类型的参数
    chunk_sizes: [1024, 1024, 512, 512, 128, 128, 128, 128]
    group_sizes: [2, 2]
    flowing_watermark: 0.10
    # schedule_policy: "load_aware"
    schedule_policy: "length_aware"
    # enable_zero_decoding: ""
    enable_zero_decoding: "--enable_zero_decoding"
    prefill_token_limit: [16384, 16384, 16384, 16384, 16384, 16384, 16384, 16384]
    prefill_processing_capacities: [7500, 7500, 3500, 3500, 7000, 7000, 7000, 7000]
    decoding_policy: "simple_flowing"
    # decoding_policy: "prophet_flowing"
    disable_async_output: [False, False, False, False, False, False, False, False] # Pure P = True
  pd_disaggregation:
    decoding_size: 2
    chunk_sizes: [1024, 1024, 512, 512, 512, 512, 512, 512]
    group_sizes: []
    enable_flowing: "" # store_true 类型的参数
    flowing_watermark: 0.05
    schedule_policy: "load_aware"
    enable_zero_decoding: ""
    prefill_token_limit: [0]
    prefill_processing_capacities: [0]
    decoding_policy: "simple_flowing"
    disable_async_output: [True, True, False, False, False, False, False, False] # Pure P = True
  chunked_prefill:
    decoding_size: 0
    chunk_sizes: [1024, 1024, 1024, 1024, 512, 512, 512, 512]
    group_sizes: []
    enable_flowing: "" # store_true 类型的参数
    flowing_watermark: 0.05
    schedule_policy: "load_aware"
    enable_zero_decoding: ""
    prefill_token_limit: [0]
    prefill_processing_capacities: [0]
    decoding_policy: "simple_flowing"
    disable_async_output: [False, False, False, False, False, False, False, False] # Pure P = True

### 平台特殊配置 (高优先级) ###
platforms: 
  "cwang-X99": # X99 双卡 3060 平台
    qps_list: [3]
    num_prompts: 32
    selected_model: 0
    world_size: 2

  "992cdc39c3cc": # 5 卡 4090 平台
    qps_list: [3]
    num_prompts: 32
    selected_model: 0
