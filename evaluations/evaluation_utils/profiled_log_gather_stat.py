#!/usr/bin/env python3
"""
Log File Processor for Profile Data

This module processes instance log files to extract and parse profile data groups
containing Batch Info, Model Level, and Engine Level information.
"""

import os
import glob
import ast
import json
from typing import List, Dict, Any, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def safe_parse_dict(dict_str: str, line_number: int, filename: str) -> Optional[Dict[str, Any]]:
    """
    Safely parse a dictionary string into a Python dict object.
    
    Args:
        dict_str: String representation of a dictionary
        line_number: Line number in the file for error reporting
        filename: Filename for error reporting
        
    Returns:
        Parsed dictionary or None if parsing fails
    """
    try:
        # First try using ast.literal_eval (safest)
        return ast.literal_eval(dict_str.strip())
    except (ValueError, SyntaxError) as e:
        logger.warning(f"ast.literal_eval failed for {filename}:{line_number} - {e}")
        try:
            # Fallback to eval (less safe but handles more cases)
            # Note: This is generally not recommended for untrusted input
            return eval(dict_str.strip())
        except Exception as e:
            logger.error(f"Failed to parse dictionary in {filename}:{line_number}: {e}")
            logger.error(f"Problematic string: {dict_str[:100]}...")
            return None


def extract_profile_data_groups(log_file_path: str) -> List[Dict[str, Any]]:
    """
    Extract profile data groups from a single log file.
    
    Args:
        log_file_path: Path to the log file
        
    Returns:
        List of dictionaries containing batch_info, model_level, and engine_level data
    """
    data_groups = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except Exception as e:
        logger.error(f"Failed to read file {log_file_path}: {e}")
        return data_groups
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for Batch Info line
        if line.startswith('[Profile] Batch Info:'):
            batch_info_line = line
            batch_info_data = None
            model_level_data = None
            engine_level_data = None
            
            # Extract batch info data
            batch_info_str = batch_info_line.split('[Profile] Batch Info:', 1)[1].strip()
            batch_info_data = safe_parse_dict(batch_info_str, i + 1, log_file_path)
            
            if batch_info_data is None:
                i += 1
                continue
            
            # Look for the next Model Level line
            j = i + 1
            while j < len(lines):
                model_line = lines[j].strip()
                if model_line.startswith('[Profile] Model Level:'):
                    model_level_str = model_line.split('[Profile] Model Level:', 1)[1].strip()
                    model_level_data = safe_parse_dict(model_level_str, j + 1, log_file_path)
                    break
                j += 1
            
            if model_level_data is None:
                i += 1
                continue
            
            # Look for the next Engine Level line after the Model Level line
            k = j + 1
            while k < len(lines):
                engine_line = lines[k].strip()
                if engine_line.startswith('[Profile] Engine Level:'):
                    engine_level_str = engine_line.split('[Profile] Engine Level:', 1)[1].strip()
                    engine_level_data = safe_parse_dict(engine_level_str, k + 1, log_file_path)
                    break
                k += 1
            
            if engine_level_data is None:
                i += 1
                continue
            
            # Create a complete data group
            data_group = {
                'batch_info': batch_info_data,
                'model_level': model_level_data,
                'engine_level': engine_level_data
            }
            
            data_groups.append(data_group)
            
            # Continue searching from after the engine level line
            i = k + 1
        else:
            i += 1
    
    return data_groups


def process_log_files(log_directory: str) -> List[Dict[str, Any]]:
    """
    Process all instance_*.log files in the specified directory.
    
    Args:
        log_directory: Path to the directory containing log files
        
    Returns:
        List of all data groups from all processed files
    """
    if not os.path.exists(log_directory):
        logger.error(f"Log directory does not exist: {log_directory}")
        return []
    
    # Find all instance_*.log files
    pattern = os.path.join(log_directory, "instance_*.log")
    log_files = glob.glob(pattern)
    
    if not log_files:
        logger.warning(f"No instance_*.log files found in {log_directory}")
        return []
    
    logger.info(f"Found {len(log_files)} log files to process")
    
    all_data_groups = []
    
    for log_file in sorted(log_files):  # Sort for consistent processing order
        logger.info(f"Processing {log_file}")
        
        file_data_groups = extract_profile_data_groups(log_file)
        
        # Add metadata about which file the data came from
        for group in file_data_groups:
            group['source_file'] = os.path.basename(log_file)
        
        all_data_groups.extend(file_data_groups)
        
        logger.info(f"Extracted {len(file_data_groups)} data groups from {os.path.basename(log_file)}")
    
    logger.info(f"Total data groups extracted: {len(all_data_groups)}")
    return all_data_groups


def save_to_json(data_groups: List[Dict[str, Any]], output_file: str) -> bool:
    """
    Save the processed data groups to a JSON file.

    Args:
        data_groups: List of data groups to save
        output_file: Path to the output JSON file

    Returns:
        True if successful, False otherwise
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data_groups, f, indent=2, ensure_ascii=False)
        logger.info(f"Data saved to {output_file}")
        return True
    except Exception as e:
        logger.error(f"Failed to save data to {output_file}: {e}")
        return False


def get_statistics(data_groups: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Generate statistics about the processed data groups.

    Args:
        data_groups: List of data groups

    Returns:
        Dictionary containing various statistics
    """
    if not data_groups:
        return {}

    files_processed: Dict[str, int] = {}
    model_level_keys: set = set()
    engine_level_keys: set = set()
    batch_info_keys: set = set()

    for group in data_groups:
        # Count by source file
        source_file = group.get('source_file', 'unknown')
        files_processed[source_file] = files_processed.get(source_file, 0) + 1

        # Collect unique keys from each level
        if 'model_level' in group and group['model_level']:
            model_level_keys.update(group['model_level'].keys())

        if 'engine_level' in group and group['engine_level']:
            engine_level_keys.update(group['engine_level'].keys())

        if 'batch_info' in group and group['batch_info']:
            batch_info_keys.update(group['batch_info'].keys())

    # Convert sets to sorted lists for JSON serialization
    stats = {
        'total_groups': len(data_groups),
        'files_processed': files_processed,
        'model_level_keys': sorted(list(model_level_keys)),
        'engine_level_keys': sorted(list(engine_level_keys)),
        'batch_info_keys': sorted(list(batch_info_keys))
    }

    return stats


def calculate_averages(data_groups: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate average values for all numerical metrics across all data groups.

    Args:
        data_groups: List of data groups containing batch_info, model_level, and engine_level data

    Returns:
        Dictionary containing average values for each numerical metric
    """
    if not data_groups:
        return {}

    # Collect all numerical values for each metric
    metric_values: Dict[str, List[float]] = {}

    def collect_numerical_values(data: Dict[str, Any], prefix: str = ""):
        """Recursively collect numerical values from nested dictionaries."""
        for key, value in data.items():
            full_key = f"{prefix}_{key}" if prefix else key

            if isinstance(value, (int, float)) and not isinstance(value, bool):
                # It's a numerical value
                if full_key not in metric_values:
                    metric_values[full_key] = []
                metric_values[full_key].append(float(value))
            elif isinstance(value, dict):
                # Recursively process nested dictionaries
                collect_numerical_values(value, full_key)
            elif isinstance(value, list):
                # Handle lists of numerical values
                for i, item in enumerate(value):
                    if isinstance(item, (int, float)) and not isinstance(item, bool):
                        list_key = f"{full_key}[{i}]"
                        if list_key not in metric_values:
                            metric_values[list_key] = []
                        metric_values[list_key].append(float(item))

    # Process each data group
    for group in data_groups:
        # Process batch_info
        if "batch_info" in group and group["batch_info"]:
            collect_numerical_values(group["batch_info"], "batch_info")

        # Process model_level
        if "model_level" in group and group["model_level"]:
            collect_numerical_values(group["model_level"], "model_level")

        # Process engine_level
        if "engine_level" in group and group["engine_level"]:
            collect_numerical_values(group["engine_level"], "engine_level")

    # Calculate averages
    averages = {}
    for metric_name, values in metric_values.items():
        if values:  # Only calculate average if we have values
            try:
                average = sum(values) / len(values)
                averages[metric_name] = {
                    "average": round(average, 6),
                    "count": len(values),
                    "min": round(min(values), 6),
                    "max": round(max(values), 6),
                }
            except (ZeroDivisionError, TypeError, ValueError) as e:
                logger.warning(f"Failed to calculate average for {metric_name}: {e}")
                continue

    return averages


def save_averages_to_file(averages: Dict[str, Any], output_file: str) -> bool:
    """
    Save the calculated averages to a JSON file.

    Args:
        averages: Dictionary containing average values
        output_file: Path to the output JSON file

    Returns:
        True if successful, False otherwise
    """
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(averages, f, indent=2, ensure_ascii=False)
        logger.info(f"Averages saved to {output_file}")
        return True
    except Exception as e:
        logger.error(f"Failed to save averages to {output_file}: {e}")
        return False


def save_dict_to_json(data: Dict[str, Any], output_file: str) -> bool:
    """
    Save a dictionary to a JSON file.

    Args:
        data: Dictionary to save
        output_file: Path to the output JSON file

    Returns:
        True if successful, False otherwise
    """
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        logger.info(f"Data saved to {output_file}")
        return True
    except Exception as e:
        logger.error(f"Failed to save data to {output_file}: {e}")
        return False


def main():
    """
    Main function to demonstrate usage of the log processor.
    """
    import argparse

    parser = argparse.ArgumentParser(description='Process instance log files to extract profile data')
    parser.add_argument(
        "--log-dir",
        default="evaluations/latency_breakdown/.cache/Qwen/Qwen2.5-14B/arxiv/2.00/hybrid/logs",
        help="Directory containing instance_*.log files (also used as output directory)",
    )

    args = parser.parse_args()

    print(f"Processing log files from: {args.log_dir}")

    # Process the log files
    data_groups = process_log_files(args.log_dir)

    if not data_groups:
        print("No data groups found!")
        return

    print(f"\nSuccessfully processed {len(data_groups)} data groups")

    # Generate and display statistics
    stats = get_statistics(data_groups)
    print(f"\nStatistics:")
    print(f"Total data groups: {stats['total_groups']}")
    print("Data groups per file:")
    for filename, count in sorted(stats['files_processed'].items()):
        print(f"  {filename}: {count}")

    print(f"\nUnique keys found:")
    print(f"Batch Info keys: {stats['batch_info_keys']}")
    print(f"Model Level keys: {stats['model_level_keys']}")
    print(f"Engine Level keys: {stats['engine_level_keys']}")

    # Calculate and display averages
    print("\nCalculating averages for numerical metrics...")
    averages = calculate_averages(data_groups)

    if averages:
        print(f"\nAverage values for numerical metrics:")
        for metric_name, metric_data in sorted(averages.items()):
            print(f"  {metric_name}:")
            print(f"    Average: {metric_data['average']}")
            print(f"    Count: {metric_data['count']}")
            print(f"    Min: {metric_data['min']}")
            print(f"    Max: {metric_data['max']}")
    else:
        print("No numerical metrics found for averaging.")

    # Display a sample of the first data group
    if data_groups:
        print("\nSample data group (first one):")
        print(json.dumps(data_groups[0], indent=2, ensure_ascii=False))

    # Save all outputs to the same directory as the log files
    output_dir = args.log_dir

    # Save processed data to JSON file
    data_output_file = os.path.join(output_dir, "processed_profile_data.json")
    if save_to_json(data_groups, data_output_file):
        print(f"\nProcessed data saved to: {data_output_file}")

    # Save statistics to JSON file
    stats_output_file = os.path.join(output_dir, "profile_statistics.json")
    if save_dict_to_json(stats, stats_output_file):
        print(f"Statistics saved to: {stats_output_file}")

    # Save averages to JSON file
    if averages:
        averages_output_file = os.path.join(output_dir, "profile_averages.json")
        if save_averages_to_file(averages, averages_output_file):
            print(f"Averages saved to: {averages_output_file}")


if __name__ == "__main__":
    main()
