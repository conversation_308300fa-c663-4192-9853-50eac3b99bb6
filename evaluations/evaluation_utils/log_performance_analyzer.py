#!/usr/bin/env python3
"""
日志性能分析脚本
解析日志文件并自动生成JSON格式的每个请求的性能指标和汇总的统计数据

用法:
    python log_performance_analyzer.py <log_directory_path>

示例:
    python log_performance_analyzer.py evaluations/latency_breakdown/.cache/Qwen/Qwen2.5-14B/arxiv/2.00/hybrid/logs/
"""

import os
import sys
import json
import re
import argparse
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class RequestMetrics:
    """请求性能指标数据类"""
    request_id: str
    prefill_length: int
    prefill_instance: int
    schedule_elapsed: float
    prefill_elapsed: float
    transfer_elapsed: float
    ttft: float
    decoding_elapsed: float

    # 从instance日志提取的数据
    scheduling_latency: Optional[float] = None
    kv_transfer_latency: Optional[float] = None
    kv_data_size_gb: Optional[float] = None

    # 计算得出的指标
    prefill_queue_time: Optional[float] = None
    prefill_execution_time: Optional[float] = None
    transfer_time: Optional[float] = None
    decoding_execution_time: Optional[float] = None

    # 新增的调度时间指标
    prefill_scheduling_time: Optional[float] = None
    decoding_scheduling_time: Optional[float] = None


class LogPerformanceAnalyzer:
    """日志性能分析器"""

    def __init__(self, log_directory: str, exclude_warmup_range: Optional[str] = None, exclude_ids: Optional[List[str]] = None):
        self.log_directory = Path(log_directory)
        self.metrics: Dict[str, RequestMetrics] = {}
        self.excluded_requests = 0
        self.profile_averages = None  # 存储profile_averages.json数据

        # 设置排除条件
        self.exclude_ids_set = set()
        if exclude_warmup_range:
            self._parse_warmup_range(exclude_warmup_range)
        if exclude_ids:
            self.exclude_ids_set.update(exclude_ids)

        # 正则表达式模式
        self.json_metrics_pattern = re.compile(
            r'\[GlobalRequest\] json metrics:\s*(.+)'
        )
        self.scheduling_latency_pattern = re.compile(
            r"Request (ID-\d+)-\d+ scheduling latency: ([\d.]+)s"
        )
        self.kv_buffer_pattern = re.compile(
            r"\[KvBuffer\] Request ID: (ID-\d+)-\d+, "
            r"Data Size: ([\d.]+) GB, Send Time: ([\d.]+) s, Total Latency: ([\d.]+) s"
        )

    def _parse_warmup_range(self, warmup_range: str) -> None:
        """解析预热范围参数，例如 '0000-0007'"""
        try:
            start_str, end_str = warmup_range.split('-')
            start_id = int(start_str)
            end_id = int(end_str)

            for i in range(start_id, end_id + 1):
                self.exclude_ids_set.add(f"{i:04d}")

        except ValueError as e:
            print(f"警告: 无效的预热范围格式 '{warmup_range}': {e}")
            print("正确格式示例: '0000-0007'")

    def _should_exclude_request(self, request_id: str) -> bool:
        """检查请求ID是否应该被排除"""
        if not self.exclude_ids_set:
            return False

        # 从request_id中提取数字ID部分，例如从 "ID-0007" 提取 "0007"
        match = re.match(r"ID-(\d+)", request_id)
        if match:
            id_part = match.group(1)
            return id_part in self.exclude_ids_set

        return False

    def parse_proxy_log(self) -> None:
        """解析proxy.log文件，提取JSON metrics数据"""
        proxy_log_path = self.log_directory / "proxy.log"

        if not proxy_log_path.exists():
            print(f"警告: proxy.log文件不存在: {proxy_log_path}")
            return

        print(f"解析proxy.log文件: {proxy_log_path}")

        try:
            with open(proxy_log_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    match = self.json_metrics_pattern.search(line)
                    if match:
                        try:
                            json_data = json.loads(match.group(1))
                            request_id = json_data['request_id']

                            # 检查是否应该排除此请求
                            if self._should_exclude_request(request_id):
                                self.excluded_requests += 1
                                continue

                            metrics = RequestMetrics(
                                request_id=request_id,
                                prefill_length=json_data['prefill_length'],
                                prefill_instance=json_data['prefill_instance'],
                                schedule_elapsed=float(json_data['schedule_elapsed']),
                                prefill_elapsed=float(json_data['prefill_elapsed']),
                                transfer_elapsed=float(json_data['transfer_elapsed']),
                                ttft=float(json_data['ttft']),
                                decoding_elapsed=float(json_data['decoding_elapsed'])
                            )

                            self.metrics[request_id] = metrics

                        except (json.JSONDecodeError, KeyError, ValueError) as e:
                            print(f"警告: 解析JSON失败 (行 {line_num}): {e}")
                            print(f"  原始数据: {match.group(1)}")

        except Exception as e:
            print(f"错误: 读取proxy.log文件失败: {e}")

    def parse_instance_logs(self) -> None:
        """解析instance开头的日志文件，提取调度延迟和传输延迟信息"""
        instance_files = list(self.log_directory.glob("instance_*.log"))

        if not instance_files:
            print("警告: 未找到instance开头的日志文件")
            return

        for instance_file in instance_files:
            print(f"解析instance日志文件: {instance_file}")
            self._parse_single_instance_log(instance_file)

    def _parse_single_instance_log(self, log_file: Path) -> None:
        """解析单个instance日志文件"""
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    # 解析调度延迟
                    scheduling_match = self.scheduling_latency_pattern.search(line)
                    if scheduling_match:
                        request_id = scheduling_match.group(1)
                        latency = float(scheduling_match.group(2))

                        # 检查是否应该排除此请求
                        if self._should_exclude_request(request_id):
                            continue

                        if request_id in self.metrics:
                            self.metrics[request_id].scheduling_latency = latency
                        else:
                            print(f"警告: 找到调度延迟但未找到对应的请求ID: {request_id}")

                    # 解析KV传输延迟
                    kv_match = self.kv_buffer_pattern.search(line)
                    if kv_match:
                        request_id = kv_match.group(1)
                        data_size = float(kv_match.group(2))
                        total_latency = float(kv_match.group(4))

                        # 检查是否应该排除此请求
                        if self._should_exclude_request(request_id):
                            continue

                        if request_id in self.metrics:
                            self.metrics[request_id].kv_transfer_latency = total_latency
                            self.metrics[request_id].kv_data_size_gb = data_size
                        else:
                            print(f"警告: 找到KV传输延迟但未找到对应的请求ID: {request_id}")

        except Exception as e:
            print(f"错误: 读取instance日志文件失败 {log_file}: {e}")

    def load_profile_averages(self) -> None:
        """加载profile_averages.json文件"""
        profile_averages_path = self.log_directory / "profile_averages.json"

        if not profile_averages_path.exists():
            print(f"警告: profile_averages.json文件不存在: {profile_averages_path}")
            return

        try:
            with open(profile_averages_path, "r", encoding="utf-8") as f:
                self.profile_averages = json.load(f)
            print(f"成功加载profile_averages.json文件: {profile_averages_path}")
        except Exception as e:
            print(f"错误: 读取profile_averages.json文件失败: {e}")

    def calculate_derived_metrics(self) -> None:
        """计算派生指标"""
        # 获取decoding调度时间百分比
        decoding_scheduling_percentage = self._get_decoding_scheduling_percentage()

        for request_id, metrics in self.metrics.items():
            try:
                # 1. 计算prefill调度时间 = schedule_elapsed (从现有的schedule_elapsed字段提取)
                metrics.prefill_scheduling_time = metrics.schedule_elapsed

                # 2. 计算decoding调度时间 = decoding_elapsed * decoding_scheduling_percentage
                if decoding_scheduling_percentage is not None:
                    metrics.decoding_scheduling_time = metrics.decoding_elapsed * (
                        decoding_scheduling_percentage / 100.0
                    )
                else:
                    metrics.decoding_scheduling_time = 0.0
                    if metrics.decoding_elapsed > 0:
                        print(
                            f"警告: 请求 {request_id} 无法获取decoding调度百分比，设置为0"
                        )

                # 3. 重新计算prefill_queue_time，移除schedule_elapsed避免重复计算
                if metrics.scheduling_latency is not None:
                    metrics.prefill_queue_time = metrics.scheduling_latency
                else:
                    metrics.prefill_queue_time = 0.0
                    print(
                        f"警告: 请求 {request_id} 缺少调度延迟数据，设置prefill_queue_time为0"
                    )

                # 4. 重新计算prefill_execution_time，移除schedule_elapsed避免重复计算
                if metrics.scheduling_latency is not None:
                    metrics.prefill_execution_time = (
                        metrics.prefill_elapsed - metrics.schedule_elapsed - metrics.scheduling_latency
                    )
                else:
                    metrics.prefill_execution_time = metrics.prefill_elapsed - metrics.schedule_elapsed

                # 5. transfer_time = Total_Latency（来自KvBuffer日志）
                if metrics.kv_transfer_latency is not None:
                    metrics.transfer_time = metrics.kv_transfer_latency
                else:
                    metrics.transfer_time = metrics.transfer_elapsed
                    if metrics.transfer_elapsed > 0:
                        print(f"警告: 请求 {request_id} 缺少KV传输延迟数据，使用transfer_elapsed")

                # 6. 重新计算decoding_execution_time，移除调度时间避免重复计算
                if metrics.decoding_scheduling_time is not None:
                    metrics.decoding_execution_time = (
                        metrics.decoding_elapsed - metrics.decoding_scheduling_time
                    )
                else:
                    metrics.decoding_execution_time = metrics.decoding_elapsed

            except Exception as e:
                print(f"错误: 计算派生指标失败 {request_id}: {e}")

    def _get_decoding_scheduling_percentage(self) -> Optional[float]:
        """从profile_averages.json中获取decoding调度时间百分比"""
        if self.profile_averages is None:
            return None

        try:
            # 查找engine_level_调度_cpu_percentage字段
            scheduling_key = "engine_level_调度_cpu_percentage"
            if scheduling_key in self.profile_averages:
                percentage_data = self.profile_averages[scheduling_key]
                if isinstance(percentage_data, dict) and "average" in percentage_data:
                    return float(percentage_data["average"])

            print(f"警告: 在profile_averages.json中未找到{scheduling_key}字段")
            return None

        except Exception as e:
            print(f"错误: 解析decoding调度百分比失败: {e}")
            return None

    def generate_statistics(self) -> Dict:
        """生成统计数据"""
        if not self.metrics:
            return {"error": "没有找到有效的性能数据"}

        # 收集所有有效的指标值
        valid_metrics = [m for m in self.metrics.values() if m.prefill_queue_time is not None]

        if not valid_metrics:
            return {"error": "没有找到有效的计算指标"}

        def safe_avg(values):
            """安全计算平均值"""
            valid_values = [v for v in values if v is not None]
            return sum(valid_values) / len(valid_values) if valid_values else 0.0

        stats = {
            "total_requests_processed": len(self.metrics),
            "excluded_requests": self.excluded_requests,
            "valid_requests": len(valid_metrics),
            "average_metrics": {
                "prefill_queue_time": safe_avg(
                    [m.prefill_queue_time for m in valid_metrics]
                ),
                "prefill_execution_time": safe_avg(
                    [m.prefill_execution_time for m in valid_metrics]
                ),
                "transfer_time": safe_avg([m.transfer_time for m in valid_metrics]),
                "decoding_execution_time": safe_avg(
                    [m.decoding_execution_time for m in valid_metrics]
                ),
                "prefill_scheduling_time": safe_avg(
                    [m.prefill_scheduling_time for m in valid_metrics]
                ),
                "decoding_scheduling_time": safe_avg(
                    [m.decoding_scheduling_time for m in valid_metrics]
                ),
            },
        }

        return stats

    def generate_quick_summary(self) -> Dict:
        """生成快速性能摘要，类似原quick_performance_summary.py的输出格式"""
        if not self.metrics:
            return {"error": "没有找到有效的性能数据"}

        valid_metrics = [
            m for m in self.metrics.values() if m.prefill_queue_time is not None
        ]

        if not valid_metrics:
            return {"error": "没有找到有效的计算指标"}

        total_count = len(valid_metrics)

        # 计算基础指标的平均值
        avg_metrics = {
            "requests": total_count,
            "avg_prefill_len": int(
                sum(m.prefill_length for m in valid_metrics) / total_count
            ),
            "avg_schedule": f"{sum(m.schedule_elapsed for m in valid_metrics) / total_count:.3f}s",
            "avg_prefill": f"{sum(m.prefill_elapsed for m in valid_metrics) / total_count:.3f}s",
            "avg_transfer": f"{sum(m.transfer_elapsed for m in valid_metrics) / total_count:.3f}s",
            "avg_ttft": f"{sum(m.ttft for m in valid_metrics) / total_count:.3f}s",
            "avg_decoding": f"{sum(m.decoding_elapsed for m in valid_metrics) / total_count:.3f}s",
        }

        return avg_metrics

    def save_detailed_results(self, output_dir: Path) -> None:
        """保存详细结果到JSON文件"""
        # 保存JSON格式
        json_file = output_dir / "detailed_metrics.json"
        json_data = []

        for metrics in self.metrics.values():
            data = asdict(metrics)
            json_data.append(data)

        try:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            print(f"详细指标已保存到: {json_file}")
        except Exception as e:
            print(f"错误: 保存JSON文件失败: {e}")

    def save_summary_statistics(self, output_dir: Path) -> None:
        """保存汇总统计数据"""
        stats = self.generate_statistics()

        # 保存详细统计摘要JSON
        summary_file = output_dir / "summary_statistics.json"
        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            print(f"汇总统计已保存到: {summary_file}")
        except Exception as e:
            print(f"错误: 保存汇总统计失败: {e}")

        if "average_metrics" in stats:
            avg = stats["average_metrics"]
            detailed_single_line_output = {
                "processed_requests": stats["total_requests_processed"],
                "excluded_requests": stats["excluded_requests"],
                "avg_prefill_queue_time": f"{avg['prefill_queue_time']:.3f}s",
                "avg_prefill_execution_time": f"{avg['prefill_execution_time']:.3f}s",
                "avg_transfer_time": f"{avg['transfer_time']:.3f}s",
                "avg_decoding_time": f"{avg['decoding_execution_time']:.3f}s",
                "avg_prefill_scheduling_time": f"{avg['prefill_scheduling_time']:.3f}s",
                "avg_decoding_scheduling_time": f"{avg['decoding_scheduling_time']:.3f}s",
            }

            print("\n=== 详细性能指标摘要 (单行格式) ===")
            print(json.dumps(detailed_single_line_output, ensure_ascii=False))

    def print_warnings_summary(self) -> None:
        """打印警告摘要"""
        missing_scheduling = 0
        missing_kv_transfer = 0

        for metrics in self.metrics.values():
            if metrics.scheduling_latency is None:
                missing_scheduling += 1
            if metrics.kv_transfer_latency is None and metrics.transfer_elapsed > 0:
                missing_kv_transfer += 1

        if missing_scheduling > 0 or missing_kv_transfer > 0:
            print(f"\n=== 数据完整性警告 ===")
            print(f"缺少调度延迟数据的请求: {missing_scheduling}/{len(self.metrics)}")
            print(f"缺少KV传输延迟数据的请求: {missing_kv_transfer}/{len(self.metrics)}")

    def analyze(self) -> None:
        """执行完整的分析流程"""
        print("开始日志性能分析...")

        # 显示排除条件信息
        if self.exclude_ids_set:
            print(f"排除预热请求ID: {sorted(self.exclude_ids_set)}")

        # 解析日志文件
        self.parse_proxy_log()
        self.parse_instance_logs()
        self.load_profile_averages()

        if not self.metrics:
            if self.excluded_requests > 0:
                print(f"警告: 所有 {self.excluded_requests} 个请求都被排除，未找到有效的性能数据")
            else:
                print("错误: 未找到任何性能数据")
            return

        print(f"成功解析 {len(self.metrics)} 个请求的性能数据")
        if self.excluded_requests > 0:
            print(f"排除了 {self.excluded_requests} 个预热请求")

        # 计算派生指标
        self.calculate_derived_metrics()

        # 保存结果
        self.save_detailed_results(self.log_directory)
        self.save_summary_statistics(self.log_directory)

        # 打印警告摘要
        self.print_warnings_summary()

        print(f"\n分析完成！结果文件已保存到: {self.log_directory}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="解析日志文件并生成性能统计报告",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
    # 分析所有请求
    python log_performance_analyzer.py evaluations/latency_breakdown/.cache/Qwen/Qwen2.5-14B/arxiv/2.00/hybrid/logs/

    # 排除预热请求（范围方式）
    python log_performance_analyzer.py --exclude-warmup-range 0000-0007 evaluations/latency_breakdown/.cache/Qwen/Qwen2.5-14B/arxiv/2.00/hybrid/logs/

    # 排除特定请求ID（列表方式）
    python log_performance_analyzer.py --exclude-ids 0000,0001,0002,0003,0004,0005,0006,0007 evaluations/latency_breakdown/.cache/Qwen/Qwen2.5-14B/arxiv/2.00/hybrid/logs/

输出文件:
    - detailed_metrics.json: 每个请求的详细性能指标
    - summary_statistics.json: 详细汇总统计数据
    - quick_summary.json: 快速性能摘要（类似原quick_performance_summary.py的输出）
        """,
    )

    parser.add_argument(
        "log_directory",
        default="evaluations/latency_breakdown/.cache/Qwen/Qwen2.5-14B/arxiv/2.00/hybrid/logs/",
        help="日志目录路径",
    )

    parser.add_argument(
        '--exclude-warmup-range',
        type=str,
        default="0000-0007",
        help='排除预热请求的ID范围，格式: 0000-0007 (排除ID-0000到ID-0007的所有请求)'
    )

    parser.add_argument(
        '--exclude-ids',
        type=str,
        help='排除特定请求ID列表，格式: 0000,0001,0002,0003 (用逗号分隔)'
    )

    args = parser.parse_args()

    # 验证目录存在
    log_dir = Path(args.log_directory)
    if not log_dir.exists():
        print(f"错误: 日志目录不存在: {log_dir}")
        sys.exit(1)

    if not log_dir.is_dir():
        print(f"错误: 指定的路径不是目录: {log_dir}")
        sys.exit(1)

    # 处理排除ID列表
    exclude_ids = None
    if args.exclude_ids:
        exclude_ids = [id_str.strip() for id_str in args.exclude_ids.split(',')]

    # 执行分析
    analyzer = LogPerformanceAnalyzer(
        args.log_directory,
        exclude_warmup_range=args.exclude_warmup_range,
        exclude_ids=exclude_ids
    )
    analyzer.analyze()


if __name__ == "__main__":
    main()
