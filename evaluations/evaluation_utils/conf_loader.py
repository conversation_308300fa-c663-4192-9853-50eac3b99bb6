import os
import sys
import yaml
import socket

from typing import Dict, Any

def load_conf(common_conf_path: str, conf_path: str) -> Dict[str, Any]:
    """载入配置文件, 生成配置对象"""
    common_conf_path = os.path.abspath(common_conf_path)
    conf_path = os.path.abspath(conf_path)
    # 读取配置
    with open(common_conf_path, "r") as f:
        common_conf = yaml.safe_load(f)
        print(f"Loaded common config from {common_conf_path}")

    with open(conf_path, "r") as f:
        conf = yaml.safe_load(f)
        print(f"Loaded config from {conf_path}")

    # 获取主机名, 和并平台相关配置
    hostname = socket.gethostname()
    platforms_common = common_conf["platforms"]
    platforms = conf["platforms"]

    if hostname in platforms_common:
        print(f"Found config for hostname {hostname} in {common_conf_path}")
        # 使用全局配置作为基础
        final_conf = common_conf.copy()

        # 用全局主机配置覆盖
        final_conf.update(platforms_common[hostname])

        # 补充本次实验配置
        final_conf.update(conf)

        # 用主机实验配置覆盖 (可为空)
        final_conf.update(platforms.get(hostname, {}))
    else:
        print(
            f"[Error] Hostname: {hostname} is not in {common_conf_path}, please do the necessary "
            f"configurations first, like python path, dataset path."
        )
        sys.exit(1)

    return final_conf